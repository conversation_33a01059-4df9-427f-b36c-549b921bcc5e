"""
Base settings to build other settings files upon.
"""

import environ

from myoperator.centrallog.formatter import CentralFormatter

ROOT_DIR = (
    environ.Path(__file__) - 3
)  # (account_api/config/settings/base.py - 3 = account_api/)  # noqa
APPS_DIR = ROOT_DIR.path("accounts")

env = environ.Env()

# ------------------------------------------------------------------------------
# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", default=False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "UTC"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# Default Date Format
DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%SZ"


# ------------------------------------------------------------------------------
# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    # Django default apps
]
THIRD_PARTY_APPS = [
    "rest_framework",
    "django_celery_results",
]
LOCAL_APPS = [
    "accounts.kyc.apps.KycConfig",
    "accounts.billing_accounts.apps.BillingAccountsConfig",
    "accounts.packages.apps.PackagesConfig",
    "accounts.services.apps.ServicesConfig",
    "accounts.products.apps.ProductsConfig",
    "accounts.cafs.apps.CafsConfig",
    "accounts.discounts.apps.DiscountsConfig",
    "accounts.invoices.apps.InvoicesConfig",
    "accounts.workflows.apps.WorkflowsConfig",
    "accounts.users.apps.UsersConfig",
    "accounts.payments.apps.PaymentsConfig",
    "accounts.leg_b.apps.LegBConfig",
    "accounts.core.apps.CoreConfig",
    "accounts.offers.apps.OffersConfig",
    "accounts.verification.apps.VerificationConfig",
    "accounts.global_packages.apps.GlobalPackagesConfig",
    "accounts.background_jobs.apps.BackgroundJobsConfig",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# ------------------------------------------------------------------------------
# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases
# noqa
DATABASES = {
    "default": {
        "ENGINE": env.str(
            "DATABASE_ENGINE", default="django.db.backends.mysql"
        ),
        "NAME": env.str("DATABASE_NAME"),
        "USER": env.str("DATABASE_USER"),
        "PASSWORD": env.str("DATABASE_PASSWORD"),
        "HOST": env.str("DATABASE_HOST"),
        "PORT": env.str("DATABASE_PORT", 3306),
    }
}


# ------------------------------------------------------------------------------
# CACHES
# ------------------------------------------------------------------------------
CACHES = {
    "default": {
        "BACKEND": env.str(
            "CACHE_BACKEND", default="django_redis.cache.RedisCache"
        ),
        "LOCATION": env("REDIS_URL"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # Mimicing memcache behavior.
            # http://niwinz.github.io/django-redis/latest/#_memcached_exceptions_behavior
            "IGNORE_EXCEPTIONS": True,
        },
        "KEY_PREFIX": "accounts",
        "VERSION": env.int("REDIS_VERSION_KEY", default=1),
    }
}


# ------------------------------------------------------------------------------
# ------------------------------------------------------------------------------
# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "config.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "config.wsgi.application"


# ------------------------------------------------------------------------------
# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = []


# ------------------------------------------------------------------------------
# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "config.health.HealthCheckMiddleware",
    "accounts.middleware.MyOperatorMiddleware",
    # https://github.com/Rhumbix/django-request-logging
    "request_logging.middleware.LoggingMiddleware",
]


# ------------------------------------------------------------------------------
# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR("media"))
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/media/"

logging_fmt = '{"logger": "%(name)s", "logtime": "%(asctime)s", "loglevel": "%(levelname)s", "message": %(message)s}'

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "myoperator": {"()": CentralFormatter, "format": logging_fmt}
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "myoperator",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
}

# https://github.com/Rhumbix/django-request-logging#request_logging_enable_colorize
REQUEST_LOGGING_ENABLE_COLORIZE = env.bool(
    "REQUEST_LOGGING_ENABLE_COLORIZE", False
)

# -------------------
# REST FRAMEWORK
# -------------------
# https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    "DATETIME_FORMAT": "%Y-%m-%dT%H:%M:%SZ",
    # 'COERCE_DECIMAL_TO_STRING': False,
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "accounts.authentication.UserAuthorization"
    ],
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.AllowAny",),
    "DEFAULT_RENDERER_CLASSES": ("config.renderer.MyOperatorRenderer",),
    "DEFAULT_PARSER_CLASSES": ("rest_framework.parsers.JSONParser",),
    "UNAUTHENTICATED_USER": None,
    "EXCEPTION_HANDLER": "accounts.exceptions.custom_exception_handler",
    "DEFAULT_PAGINATION_CLASS": "accounts.pagination.CustomPagination",
    "PAGE_SIZE": 10,
}

# ------------------------------------------------------------------------------
# Celery
# ------------------------------------------------------------------------------
if USE_TZ:
    # http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-timezone
    CELERY_TIMEZONE = TIME_ZONE
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-broker_url
CELERY_BROKER_URL = env("CELERY_BROKER_URL")
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-result_backend
CELERY_RESULT_BACKEND = "django-db"
CELERY_RESULT_EXTENDED = True
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-accept_content
CELERY_ACCEPT_CONTENT = ["json"]
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-task_serializer
CELERY_TASK_SERIALIZER = "json"
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#std:setting-result_serializer
CELERY_RESULT_SERIALIZER = "json"
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#task-time-limit
CELERY_TASK_TIME_LIMIT = 5 * 60
# http://docs.celeryproject.org/en/latest/userguide/configuration.html#task-soft-time-limit
CELERY_TASK_SOFT_TIME_LIMIT = 60
# https://docs.celeryq.dev/en/latest/userguide/configuration.html#task-default-queue
CELERY_TASK_DEFAULT_QUEUE = "accounts"
CELERY_TASK_MAX_RETRIES = env.int("CELERY_TASK_MAX_RETRIES", default=3)

# Default API Request Timeout
# ------------------------------------------------------------------------------
API_TIMEOUT = env.int("API_TIMEOUT", default=5)

# Number System
# ------------------------------------------------------------------------------
NUMBER_SYSTEM_API_URL = env.str("NUMBER_SYSTEM_API_URL")
NUMBER_SYSTEM_API_TOKEN = env.str("NUMBER_SYSTEM_API_TOKEN")
NUMBER_SYSTEM_API_TIMEOUT = env.int("NUMBER_SYSTEM_API_TIMEOUT", default=5)

# Memcache API
# ------------------------------------------------------------------------------
MEMCACHE_API_URL = env.str("MEMCACHE_API_URL")
MEMCACHE_API_URL_US = env.str("MEMCACHE_API_URL_US")

# Account API URL
# ------------------------------------------------------------------------------
ACCOUNT_API_V1_HOST = env.str("ACCOUNT_API_V1_HOST")
ACCOUNT_API_V1_SALT = env.str("ACCOUNT_API_V1_SALT")

INTERNAL_API_USER_ID = env.str("INTERNAL_API_USER_ID")

# ------------------------------------------------------------------------------
# AWS Config
# ------------------------------------------------------------------------------
AWS_ACCESS_KEY_ID = env.str("DJANGO_AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = env.str("DJANGO_AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION = env.str("DJANGO_AWS_DEFAULT_REGION")

# ------------------------------------------------------------------------------
# SNS Topic ARN
# ------------------------------------------------------------------------------
ACCOUNTS_SNS_ARN = env.str("ACCOUNTS_SNS_ARN")
SNS_EVENT_AUTH_USER_PWD = env.str("SNS_EVENT_AUTH_USER_PWD", default="test:123")

# MyOperator API
# ------------------------------------------------------------------------------
MYOPERATOR_API_URL = env.str("MYOPERATOR_API_URL")
MYOPERATOR_API_URL_US = env.str("MYOPERATOR_API_URL_US")
MYOPERATOR_API_SALT = env.str("MYOPERATOR_API_SALT")

# Account Activation Constants
# ------------------------------------------------------------------------------
REACTIVATE_NOTE = env.str("REACTIVATE_NOTE", default="REACTIVATED")
DEACTIVATE_NOTE = env.str("DEACTIVATE_NOTE", default="DEACTIVATED")
SUSPEND_NOTE = env.str("SUSPEND_NOTE", default="SUSPENDED")
FRAUD_NOTE = env.str("FRAUD_NOTE", default="FRAUD")

# Cron Users
# ------------------------------------------------------------------------------
MR_CRON = env.str("MR_CRON", "cd182d53-baa3-11e9-903b-027cf9c72897")  # noqa
ST_CRON = env.str("ST_CRON", "cd19bcbd-baa3-11e9-903b-027cf9c72897")  # noqa
AS_CRON = env.str("AS_CRON", "cd1a34e6-baa3-11e9-903b-027cf9c72897")  # noqa
DD_CRON = env.str("DD_CRON", "60773f98-3ffa-488b-9b2b-08501697598b")  # noqa
CL_CRON = env.str("CL_CRON", "cd1597e9-baa3-11e9-903b-027cf9c72897")  # noqa


# Feature Update SNS ARN
# ------------------------------------------------------------------------------
SNS_ARN_FEATURE_UPDATE = env.str("SNS_ARN_FEATURE_UPDATE")
SNS_ARN_ACCOUNT_FEATURE = env.str("SNS_ARN_ACCOUNT_FEATURE")


ACCOUNTS_PAYMENT_API = env("ACCOUNTS_PAYMENT_API")


# Chat API
# ------------------------------------------------------------------------------
CHAT_API_CONFIG = {
    "HOST": env("CHAT_API_URL"),
    "TIMEOUT": env.int("CHAT_API_TIMEOUT", default=5),
}

# Resource Refund Config
# ------------------------------------------------------------------------------
RESOURCE_REFUND_CONFIG = {
    "MAX_REFUND_DAYS": env.int("RESOURCE_REFUND_MAX_DAYS", 7),
    "REFUND_AFTER": env.int("RESOURCE_REFUND_AFTER", 86400),
}

# Cache Config
# ------------------------------------------------------------------------------
PENDING_USAGE_CACHE_EXPIRY_SECONDS = env.int(
    "PENDING_USAGE_CACHE_EXPIRY_SECONDS", 600  # 10 minutes
)

# ------------------------------------------------------------------------------
# Rechage Contact Config
# ------------------------------------------------------------------------------
RECHARGE_CONTACT_NAME = env.str("RECHARGE_CONTACT_NAME")
RECHARGE_CONTACT_EMAIL = env.str("RECHARGE_CONTACT_EMAIL")
RECHARGE_CONTACT_NUMBER = env.str("RECHARGE_CONTACT_NUMBER")

ACCOUNT_PAYMENT_GENERATION_API_URL = env("ACCOUNT_PAYMENT_GENERATION_API_URL")

CREDIT_LIMIT_EMAIL_ID = env.str("CREDIT_LIMIT_EMAIL_ID")
CREDIT_LIMIT_EMAIL_NAME = env.str("CREDIT_LIMIT_EMAIL_NAME")

TEMPLATE = {
    "LOW_BALANCE": env.str(
        "LOW_BALANCE_TEMPLATE", default="credit-card-payment-failure"
    ),
    "SERVICE_SUSPEND": env.str(
        "SERVICE_SUSPEND_TEMPLATE", default="account-suspension"
    ),
    "CREDIT_LIMIT": env.str(
        "CREDIT_LIMIT_TEMPLATE", default="low-account-balance"
    ),
    "SERVICE_SUSPEND_FAILURE": env.str(
        "SERVICE_SUSPEND_FAILURE_TEMPLATE",
        default="credit-card-payment-failure-suspension",
    ),
}

HEYO_PRODUCT_ID = env.str("HEYO_PRODUCT_ID", "61cab45237321971")
SMSG_API_HOST = env.str("SMSG_API_HOST")
# Truecaller API HOST
# ----------------
TREUCALLER_API_HOST = env.str("TREUCALLER_API_HOST")

HEYO_CREDIT_LIMIT = env.int("HEYO_CREDIT_LIMIT", 200)

# Bubble API HOST
# ----------------
BUBBLE_API_HOST_CONFIG = {
    "BUBBLE_CONTACT_SYNC": env.str("BUBBLE_CONTACT_SYNC_API_HOST")
}

# ------------------------------------------------------------------------------
# Event Billing
# ------------------------------------------------------------------------------
BILLING_EVENT_SQS_QUEUE_URL = env.str("BILLING_EVENT_SQS_QUEUE_URL")
BILLING_EVENT_SQS_MAX_ITER = env.int("BILLING_EVENT_SQS_MAX_ITER", default=1000)


# ------------------------------------------------------------------------------
# Surepass
# ------------------------------------------------------------------------------
SUREPASS_API_CONFIG = {
    "HOST": env.str("SUREPASS_API_HOST"),
    "TOKEN": env.str("SUREPASS_API_TOKEN"),
    "TIMEOUT": env.int("SUREPASS_API_TIMEOUT", default=20),
}


SMSG_API_CONFIG = {
    "HOST": env.url("SMSG_API_HOST"),
    "TIMEOUT": env.int("SMSG_API_TIMEOUT", default=20),
}
