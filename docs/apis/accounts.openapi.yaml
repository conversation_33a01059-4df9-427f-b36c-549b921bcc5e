openapi: 3.0.1
x-stoplight:
  id: hleepgflnc8nn
info:
  title: Account API
  version: '1.0'
  description: ''
  contact:
    name: <PERSON><PERSON><PERSON>
    url: ''
servers:
  - url: 'https://accountapi-v2.myoperator.dev/'
    description: Dev
  - url: 'https://accountapi-v2.myoperator-stage.co/'
    description: Stage
paths:
  /offers/:
    get:
      summary: Offers List
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        product_id:
                          type: string
                        custom_package_id:
                          type: string
                        package_code:
                          type: string
                        name:
                          type: string
                        code:
                          type: string
                        value:
                          type: string
                        term:
                          type: string
                        type:
                          type: string
                        status:
                          type: string
                        min_amount:
                          type: string
                        max_amount:
                          type: string
                        custom_package:
                          type: object
                          properties:
                            id:
                              type: string
                            parent_package_id:
                              type: string
                            package_name:
                              type: string
                            custom_code:
                              type: string
                            package_rent:
                              type: string
                            payment_cycle:
                              type: integer
                            discount:
                              type: object
                              properties:
                                name:
                                  type: string
                                code:
                                  type: string
                                term:
                                  type: integer
                                model:
                                  type: integer
                                apply_on:
                                  type: integer
                                period:
                                  type: integer
                                value:
                                  type: string
                        expiry:
                          type: string
                        created:
                          type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 86
                      per_page: 10
                      total_pages: 9
                      current: 1
                    data:
                      - id: 65c06b4daac07712
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: M56E42
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-05T04:59:57Z'
                      - id: 65c06b2d9460a138
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: JWSY77
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-05T04:59:26Z'
                      - id: 65bce666e9ff5881
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: W9ZDQY
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:56:06Z'
                      - id: 65bce6496e831855
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8f71665942144
                        package_code: 65a8f71665942144
                        name: pay 200 get 350
                        code: 2D73ZC
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8f71665942144
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: gcpKNn
                          package_rent: '499.00'
                          payment_cycle: 12
                          discount:
                            name: Standard (Monthly)- customplan
                            code: THNCG8
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1800.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:55:37Z'
                      - id: 65bce613c23ab693
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: ZLP8T9
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:54:43Z'
                      - id: 65b9c7084057196
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8f71665942144
                        package_code: 65a8f71665942144
                        name: pay 200 get 350
                        code: ZERZ5E
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8f71665942144
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: gcpKNn
                          package_rent: '499.00'
                          payment_cycle: 12
                          discount:
                            name: Standard (Monthly)- customplan
                            code: THNCG8
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1800.000'
                        expiry: '2024-02-02T12:44:53Z'
                        created: '2024-01-31T04:05:28Z'
                      - id: 65b8d389e5701397
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: QHCZSJ
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T10:46:34Z'
                      - id: 65b8d196c7c2e434
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: 4L5MWB
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T10:38:14Z'
                      - id: 65b8c6fe57b10901
                        product_id: 58ee700daca3a812
                        custom_package_id: 65ae1bace9848818
                        package_code: 65ae1bace9848818
                        name: pay 200 get 350
                        code: U7SCVT
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65ae1bace9848818
                          parent_package_id: 0933724c-f403-4b09-9d6d-499e06bd74aa
                          package_name: Lite- 3C Tollfree
                          custom_code: lqgDcn
                          package_rent: '3450.00'
                          payment_cycle: 12
                          discount:
                            name: Lite- 3C Tollfree- customplan
                            code: B9TLF4
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '12000.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T09:53:02Z'
                      - id: 65b8c6f446cf8702
                        product_id: 58ee700daca3a812
                        custom_package_id: 65ae1bace9848818
                        package_code: 65ae1bace9848818
                        name: pay 200 get 350
                        code: DQ25FB
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65ae1bace9848818
                          parent_package_id: 0933724c-f403-4b09-9d6d-499e06bd74aa
                          package_name: Lite- 3C Tollfree
                          custom_code: lqgDcn
                          package_rent: '3450.00'
                          payment_cycle: 12
                          discount:
                            name: Lite- 3C Tollfree- customplan
                            code: B9TLF4
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '12000.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T09:52:53Z'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 86
                      per_page: 10
                      total_pages: 9
                      current: 1
                    data:
                      - id: 65c06b4daac07712
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: M56E42
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-05T04:59:57Z'
                      - id: 65c06b2d9460a138
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: JWSY77
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-05T04:59:26Z'
                      - id: 65bce666e9ff5881
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8cd82e8d84186
                        package_code: 65a8cd82e8d84186
                        name: Offer 2
                        code: W9ZDQY
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8cd82e8d84186
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: 7TvMYx
                          package_rent: '499.00'
                          payment_cycle: 3
                          discount:
                            name: Standard (Monthly)- customplan
                            code: GKHFWS
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1050.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:56:06Z'
                      - id: 65bce6496e831855
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8f71665942144
                        package_code: 65a8f71665942144
                        name: pay 200 get 350
                        code: 2D73ZC
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8f71665942144
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: gcpKNn
                          package_rent: '499.00'
                          payment_cycle: 12
                          discount:
                            name: Standard (Monthly)- customplan
                            code: THNCG8
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1800.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:55:37Z'
                      - id: 65bce613c23ab693
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: ZLP8T9
                        value: '0.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-02-02T12:54:43Z'
                      - id: 65b9c7084057196
                        product_id: 58ee700daca3a812
                        custom_package_id: 65a8f71665942144
                        package_code: 65a8f71665942144
                        name: pay 200 get 350
                        code: ZERZ5E
                        value: '0.000'
                        term: flat
                        type: package
                        status: inactive
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65a8f71665942144
                          parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                          package_name: Standard (Monthly)
                          custom_code: gcpKNn
                          package_rent: '499.00'
                          payment_cycle: 12
                          discount:
                            name: Standard (Monthly)- customplan
                            code: THNCG8
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '1800.000'
                        expiry: '2024-02-02T12:44:53Z'
                        created: '2024-01-31T04:05:28Z'
                      - id: 65b8d389e5701397
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: QHCZSJ
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T10:46:34Z'
                      - id: 65b8d196c7c2e434
                        product_id: 58ee700daca3a812
                        custom_package_id: 62bc2ca8840e0286
                        package_code: 62bc2ca8840e0286
                        name: pay 200 get 350
                        code: 4L5MWB
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 62bc2ca8840e0286
                          parent_package_id: 5985cbd3a7edf783
                          package_name: Growing Business
                          custom_code: dG7Y9h
                          package_rent: '3000.00'
                          payment_cycle: 12
                          discount:
                            name: Growing Business- customplan
                            code: 3NVFDC
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '240.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T10:38:14Z'
                      - id: 65b8c6fe57b10901
                        product_id: 58ee700daca3a812
                        custom_package_id: 65ae1bace9848818
                        package_code: 65ae1bace9848818
                        name: pay 200 get 350
                        code: U7SCVT
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65ae1bace9848818
                          parent_package_id: 0933724c-f403-4b09-9d6d-499e06bd74aa
                          package_name: Lite- 3C Tollfree
                          custom_code: lqgDcn
                          package_rent: '3450.00'
                          payment_cycle: 12
                          discount:
                            name: Lite- 3C Tollfree- customplan
                            code: B9TLF4
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '12000.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T09:53:02Z'
                      - id: 65b8c6f446cf8702
                        product_id: 58ee700daca3a812
                        custom_package_id: 65ae1bace9848818
                        package_code: 65ae1bace9848818
                        name: pay 200 get 350
                        code: DQ25FB
                        value: '110.000'
                        term: flat
                        type: package
                        status: active
                        min_amount: '0.000'
                        max_amount: '1.000'
                        custom_package:
                          id: 65ae1bace9848818
                          parent_package_id: 0933724c-f403-4b09-9d6d-499e06bd74aa
                          package_name: Lite- 3C Tollfree
                          custom_code: lqgDcn
                          package_rent: '3450.00'
                          payment_cycle: 12
                          discount:
                            name: Lite- 3C Tollfree- customplan
                            code: B9TLF4
                            term: 1
                            model: 1
                            apply_on: 1
                            period: 1
                            value: '12000.000'
                        expiry: '2026-12-31T00:00:00Z'
                        created: '2024-01-30T09:52:53Z'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      operationId: get-offers
      description: |
        Get Offers list based on filters and sorting


        To filter the offers pass the field detail in query param.

        Eg. to fetch active offers 

        /offers/?status=active

        Available filter params:- *status*, *name*, *code*, *is_expired*, *package_rent*

        name: search case insensitive in offer's name. Eg name=pay. Result: name contains get 50 pay 100

        sort: name, created
      parameters:
        - schema:
            type: number
          in: query
          name: page
          description: page_number (use current variable from response pagination array)
          required: true
        - schema:
            type: string
          in: query
          name: ordering
          description: '-created'
        - schema:
            type: string
          in: query
          name: code
          description: filter by code
        - schema:
            type: string
          in: query
          name: status
          description: filter by status
        - schema:
            type: string
          in: query
          name: name
          description: filter by name
        - schema:
            type: boolean
          in: query
          name: is_expired
          description: offer expired
        - schema:
            type: string
          in: query
          name: package_rent
          description: rent of the package
      tags:
        - Offers
    post:
      summary: Create Offer
      operationId: post-offers
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Offer created.
                    data:
                      id: abc123xyz
                      product_id: xyz123
                      name: pay 200 get 350
                      code: ABC123
                      value: '150.00'
                      term: flat
                      status: active
                      min_amount: '200.00'
                      max_amount: '500.00'
                      expiry: '2026-12-31 00:00:00'
                      created: '2023-03-03 12:00:00'
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      product_id:
                        type: string
                      name:
                        type: string
                      code:
                        type: string
                      value:
                        type: string
                      term:
                        type: string
                      status:
                        type: string
                      min_amount:
                        type: string
                      max_amount:
                        type: string
                      expiry:
                        type: string
                      created:
                        type: string
                      type:
                        x-stoplight:
                          id: 9gdply2xc5g8h
                        enum:
                          - credit
                          - package
                      '':
                        type: string
                        x-stoplight:
                          id: lj6azp9hi9rh2
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Offer created.
                    data:
                      id: abc123xyz
                      product_id: xyz123
                      name: pay 200 get 350
                      code: ABC123
                      value: '150.00'
                      term: flat
                      type: credit
                      status: active
                      min_amount: '200.00'
                      max_amount: '500.00'
                      expiry: '2026-12-31 00:00:00'
                      created: '2023-03-03 12:00:00'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      name:
                        type: array
                        items:
                          type: object
                          properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: Validation Failed
                    errors:
                      name: []
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0002
                    message: Validation Failed
                    errors:
                      name: []
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      description: |
        Create Offer
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  name: pay 200 get 350
                  code: ABC123
                  value: '150.00'
                  term: flat
                  min_amount: '200.00'
                  max_amount: '500.00'
                  expiry: '2026-12-31 00:00:00'
              required:
                - name
                - expiry
                - product_id
                - type
              properties:
                name:
                  type: string
                value:
                  type: string
                  description: Value should not exceed more than 99 if term is selected as percentage
                term:
                  type: string
                  description: |-
                    Term will have two choices 
                    1. flat
                    2. percent
                  enum:
                    - flat
                    - percent
                min_amount:
                  type: string
                  description: Should not be greater than max_amount
                max_amount:
                  type: string
                  description: should not be less than min_amount
                expiry:
                  type: string
                  description: Expiry date should be greater then the current date
                product_id:
                  type: string
                  x-stoplight:
                    id: ebn4p2dekttrs
                status:
                  type: string
                  x-stoplight:
                    id: 1g8ghrs42sddw
                  enum:
                    - active
                    - inactive
                type:
                  x-stoplight:
                    id: 66p0ebmcstzf3
                  enum:
                    - credit
                    - package
                custom_package_id:
                  type: string
                  x-stoplight:
                    id: j02mwzp2aaqp8
            examples:
              Credit:
                value:
                  name: pay 200 get 350
                  value: '150.00'
                  term: flat
                  min_amount: '200.00'
                  max_amount: '500.00'
                  expiry: '2026-12-31T00:00:00Z'
                  product: abc345
                  type: credit
              Package:
                value:
                  name: pay 200 get 350
                  expiry: '2026-12-31T00:00:00Z'
                  product: abc345
                  custom_package_id: abc123
                  type: package
      tags:
        - Offers
  '/offers/{id}':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
        description: Offer ID
    get:
      summary: Offer Detail
      operationId: get-offers-offer_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      product_id:
                        type: string
                      custom_package_id:
                        type: string
                      name:
                        type: string
                      code:
                        type: string
                      value:
                        type: string
                      term:
                        type: string
                      type:
                        type: string
                      status:
                        type: string
                      min_amount:
                        type: string
                      max_amount:
                        type: string
                      custom_package:
                        type: object
                        properties:
                          id:
                            type: string
                          parent_package_id:
                            type: string
                          package_name:
                            type: string
                          custom_code:
                            type: string
                          package_rent:
                            type: string
                          payment_cycle:
                            type: integer
                          discount:
                            type: object
                            properties:
                              name:
                                type: string
                              code:
                                type: string
                              term:
                                type: integer
                              model:
                                type: integer
                              apply_on:
                                type: integer
                              period:
                                type: integer
                              value:
                                type: string
                      expiry:
                        type: string
                      created:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: 65b9c7084057196
                      product_id: 58ee700daca3a812
                      custom_package_id: 65a8f71665942144
                      name: pay 200 get 350
                      code: ZERZ5E
                      value: '0.000'
                      term: flat
                      type: package
                      status: inactive
                      min_amount: '0.000'
                      max_amount: '1.000'
                      custom_package:
                        id: 65a8f71665942144
                        parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                        package_name: Standard (Monthly)
                        custom_code: gcpKNn
                        package_rent: '499.00'
                        payment_cycle: 12
                        discount:
                          name: Standard (Monthly)- customplan
                          code: THNCG8
                          term: 1
                          model: 1
                          apply_on: 1
                          period: 1
                          value: '1800.000'
                      expiry: '2024-02-02T12:44:53Z'
                      created: '2024-01-31T04:05:28Z'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: 65b9c7084057196
                      product_id: 58ee700daca3a812
                      custom_package_id: 65a8f71665942144
                      name: pay 200 get 350
                      code: ZERZ5E
                      value: '0.000'
                      term: flat
                      type: package
                      status: inactive
                      min_amount: '0.000'
                      max_amount: '1.000'
                      custom_package:
                        id: 65a8f71665942144
                        parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                        package_name: Standard (Monthly)
                        custom_code: gcpKNn
                        package_rent: '499.00'
                        payment_cycle: 12
                        discount:
                          name: Standard (Monthly)- customplan
                          code: THNCG8
                          term: 1
                          model: 1
                          apply_on: 1
                          period: 1
                          value: '1800.000'
                      expiry: '2024-02-02T12:44:53Z'
                      created: '2024-01-31T04:05:28Z'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Offer not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Offer not found
                    errors: {}
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      description: Api for offer details based on offer id
      parameters: []
      x-internal: false
      tags:
        - Offers
      x-stoplight:
        id: pam95xyi1ih07
    put:
      summary: Edit offer
      operationId: put-offers-offer_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      code:
                        type: string
                      value:
                        type: string
                      term:
                        type: string
                      status:
                        type: string
                      min_amount:
                        type: string
                      max_amount:
                        type: string
                      expiry:
                        type: string
                      created:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Updated successfully.
                    data:
                      id: abc123xyz
                      name: pay 200 get 350
                      code: ABC123
                      value: '150.00'
                      term: flat
                      status: Active
                      min_amount: '200.00'
                      max_amount: '500.00'
                      expiry: '2026-12-31 00:00:00'
                      created: '2023-03-03 12:00:00'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Updated successfully.
                    data:
                      id: abc123xyz
                      name: pay 200 get 350
                      code: ABC123
                      value: '150.00'
                      term: flat
                      status: Active
                      min_amount: '200.00'
                      max_amount: '500.00'
                      expiry: '2026-12-31T00:00:00Z'
                      created: '2023-03-03T12:00:00Z'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: ''
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      description: ''
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  name: pay 200 get 350
                  value: '150.00'
                  term: flat
                  min_amount: '200.00'
                  max_amount: '500.00'
                  expiry: '2026-12-31 00:00:00'
                  status: '0'
              properties:
                name:
                  type: string
                value:
                  type: string
                term:
                  type: string
                  enum:
                    - flat
                    - percentage
                min_amount:
                  type: string
                max_amount:
                  type: string
                expiry:
                  type: string
                  format: date-time
                status:
                  type: string
                  enum:
                    - Active
                    - Inactive
                custom_package_id:
                  x-stoplight:
                    id: qxkbqeeh3g5aa
                  enum:
                    - package
                    - cre
            examples:
              Example 1:
                value:
                  name: pay 200 get 350
                  value: '150.00'
                  term: flat
                  min_amount: '200.00'
                  max_amount: '500.00'
                  expiry: '2026-12-31T00:00:00Z'
                  status: Active
                  custom_package_id: null
      tags:
        - Offers
      x-stoplight:
        id: 1b25ydxvxnvjb
    patch:
      summary: Enable/Disable Offer
      operationId: patch-offers-offer_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      product_id:
                        type: string
                      custom_package_id:
                        type: string
                      name:
                        type: string
                      code:
                        type: string
                      value:
                        type: string
                      term:
                        type: string
                      type:
                        type: string
                      status:
                        type: string
                      min_amount:
                        type: string
                      max_amount:
                        type: string
                      custom_package:
                        type: object
                        properties:
                          id:
                            type: string
                          parent_package_id:
                            type: string
                          package_name:
                            type: string
                          custom_code:
                            type: string
                          package_rent:
                            type: string
                          payment_cycle:
                            type: integer
                          discount:
                            type: object
                            properties:
                              name:
                                type: string
                              code:
                                type: string
                              term:
                                type: integer
                              model:
                                type: integer
                              apply_on:
                                type: integer
                              period:
                                type: integer
                              value:
                                type: string
                      expiry:
                        type: string
                      created:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: 65b7495bbcab1927
                      product_id: 58ee700daca3a812
                      custom_package_id: 65a8f71665942144
                      name: pay1 200 get 3500
                      code: YTEFDP
                      value: '10.560'
                      term: flat
                      type: package
                      status: inactive
                      min_amount: '400.700'
                      max_amount: '500.850'
                      custom_package:
                        id: 65a8f71665942144
                        parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                        package_name: Standard (Monthly)
                        custom_code: gcpKNn
                        package_rent: '499.00'
                        payment_cycle: 12
                        discount:
                          name: Standard (Monthly)- customplan
                          code: THNCG8
                          term: 1
                          model: 1
                          apply_on: 1
                          period: 1
                          value: '1800.000'
                      expiry: '2024-02-02T12:44:53Z'
                      created: '2024-01-29T06:44:44Z'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: 65b7495bbcab1927
                      product_id: 58ee700daca3a812
                      custom_package_id: 65a8f71665942144
                      name: pay1 200 get 3500
                      code: YTEFDP
                      value: '10.560'
                      term: flat
                      type: package
                      status: inactive
                      min_amount: '400.700'
                      max_amount: '500.850'
                      custom_package:
                        id: 65a8f71665942144
                        parent_package_id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                        package_name: Standard (Monthly)
                        custom_code: gcpKNn
                        package_rent: '499.00'
                        payment_cycle: 12
                        discount:
                          name: Standard (Monthly)- customplan
                          code: THNCG8
                          term: 1
                          model: 1
                          apply_on: 1
                          period: 1
                          value: '1800.000'
                      expiry: '2024-02-02T12:44:53Z'
                      created: '2024-01-29T06:44:44Z'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: ''
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  status: Active/Inactive
              properties:
                status:
                  type: string
              required:
                - status
            examples:
              Example 1:
                value:
                  status: Active/Inactive
      description: ''
      tags:
        - Offers
      x-stoplight:
        id: a4wqdab6mwttn
  '/offers/payment/{payment_id}':
    get:
      summary: Payment offer details
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      offer_id:
                        type: string
                      status:
                        type: string
                      comment:
                        type: string
                      created:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: abc456
                      offer_id: abc123xyz
                      status: Pending
                      comment: some comment
                      created: '2023-03-03 18:00:00'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: abc456
                      offer_id: abc123xyz
                      status: Pending
                      comment: some comment
                      created: '2023-03-03T18:00:00Z'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: ''
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      operationId: get-offers-payment
      parameters: []
      description: Get details of payment offer
      tags:
        - Offers
      x-stoplight:
        id: y31ftr13neoyj
    parameters:
      - schema:
          type: string
        name: payment_id
        in: path
        required: true
        description: Payment ID
  '/offers/{id}/payment':
    get:
      summary: Offer Payment List
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: '200'
                    message: ''
                    data:
                      next: 'http://example.com'
                      previous: 'http://example.com'
                      count: 0
                      records:
                        - payment_id: abc123xyz
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      next:
                        type: string
                      previous:
                        type: string
                      count:
                        type: integer
                      records:
                        type: array
                        items:
                          type: object
                          properties:
                            payment_id:
                              type: string
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 9
                      per_page: 10
                      total_pages: 1
                      current: 1
                    data:
                      - id: abc123xyz
                        payment_id: abc456xyz
                      - id: pqr123xyz
                        payment_id: abc789xyz
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: ''
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      operationId: get-offers-offer_id-payment
      description: list of payment details of this offer with pagination
      parameters:
        - schema:
            type: string
          in: query
          name: page
          description: page_number (use current variable from response pagination array)
          required: true
        - schema:
            type: string
          in: query
          name: sort
          description: sort field
        - schema:
            type: string
          in: query
          name: order
          description: order by (asc/desc)
      tags:
        - Offers
        - Payment
      x-stoplight:
        id: k4mdsm5dzgcei
    post:
      summary: Link offer payment
      operationId: post-offers-offer_id-payment
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Offer linked with payment.
                    data: {}
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Offer linked with payment.
                    data: {}
            application/xml:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Offer linked with payment.
                    data: {}
              examples:
                Example 1:
                  value: |-
                    {
                      "status": "success",
                      "code": 200,
                      "message": "Offer linked with payment.",
                      "data": {}
                    }
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0002
                    message: Invalid ID
                    errors: {}
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0001
                    message: Interal Server Error
                    errors:
                      detail: ''
      description: To link payment with offer
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1a:
                  payment_id: abc123
              properties:
                payment_id:
                  type: string
              required:
                - payment_id
            examples:
              Example 1:
                value:
                  payment_id: abc123
      tags:
        - Payment
      x-stoplight:
        id: w9hx7nuvwn06j
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
        description: Offer ID
  /verification/rule:
    post:
      summary: Create Rule
      operationId: post-verification-rule
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 201
                    message: Rule created
                    data:
                      id: 644a77dd8df49430
              examples:
                Example 1:
                  value:
                    status: success
                    code: 201
                    message: Rule created
                    data:
                      id: 644a77dd8df49430
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  rule: max_allowed
                  allowed: 10
                  time: 3600
                  product: heyoin
                  resource: aadhar
              properties:
                rule:
                  type: string
                allowed:
                  type: integer
                time:
                  type: integer
                product_id:
                  type: string
                  description: |
                    ID of the product 
                  x-stoplight:
                    id: xihjul5t9rbqf
                resource:
                  type: string
                  description: 'type of resource Ex: aadhar,android,contact'
                resource_id:
                  type: string
                  x-stoplight:
                    id: n83cyhk5rzyhq
              required:
                - rule
                - allowed
                - time
                - product_id
                - resource
            examples:
              Example 1:
                value:
                  rule: max_allowed
                  allowed: 10
                  time: 3600
                  product_id: abc123
                  resource: aadhar
                  resource_id: xyz456
        description: ''
      tags:
        - Verification
    get:
      summary: Verification Rule Listing
      operationId: get-verification-rule
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        allowed:
                          type: integer
                        time:
                          type: integer
                        rule:
                          type: string
                        product_id:
                          type: string
                        resouce:
                          type: string
                        resource_id:
                          type: string
                        status:
                          type: string
                        created:
                          type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 9
                      per_page: 10
                      total_pages: 1
                      current: 1
                    data:
                      - id: abc123
                        allowed: 0
                        time: 1379415
                        rule: max_allowed
                        product_id: heyoin
                        resouce: aadhar
                        resource_id: xyz123
                        status: active
                        created: '2023-03-03 12:00:00'
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 9
                      per_page: 10
                      total_pages: 1
                      current: 1
                    data:
                      - id: abc123
                        allowed: 0
                        time: 1379415
                        rule: max_allowed
                        product_id: heyoin
                        resouce: aadhar
                        resource_id: xyz123
                        status: active
                        created: '2023-03-03T12:00:00Z'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
      description: Listing with filters and sorting
      parameters:
        - schema:
            type: string
          in: query
          name: product_id
          description: 'product short code Ex: heyoin, myopin'
        - schema:
            type: string
          in: query
          name: resource
          description: 'Ex: aadhar,android,contact'
        - schema:
            type: string
          in: query
          name: resource_id
        - schema:
            type: string
          in: query
          name: rule
          description: 'Ex: max_allowed,max_blacklisted'
        - schema:
            type: string
          in: query
          name: status
          description: active/inactive
        - schema:
            type: string
          in: query
          name: sort
          description: created
        - schema:
            type: string
          in: query
          name: order
          description: asc/desc
      tags:
        - Verification
  '/verification/{product}/{resource}/rules':
    get:
      summary: Verification Rules
      tags:
        - Verification
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  data:
                    type: object
                    properties:
                      max_allowed:
                        type: array
                        items:
                          type: object
                          properties:
                            allowed:
                              type: integer
                            time:
                              type: string
                      max_blacklisted:
                        type: array
                        items:
                          type: object
                          properties:
                            allowed:
                              type: integer
                            time:
                              type: integer
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    data:
                      max_allowed:
                        - allowed: 0
                          time: null
                      max_blacklisted:
                        - allowed: 3
                          time: 7776000
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    data:
                      max_allowed:
                        - allowed: 0
                          time: 'null'
                      max_blacklisted:
                        - allowed: 3
                          time: 7776000
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 403
                    message: Invalid token
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Offer not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
        '500':
          $ref: '#/components/responses/500'
      operationId: get-verification
      parameters:
        - schema:
            type: string
          in: query
          name: resource_id
          required: true
    parameters:
      - schema:
          type: string
        name: product
        in: path
        required: true
        description: 'Slug of product Ex: heyoin'
      - schema:
          type: string
        name: resource
        in: path
        required: true
        description: 'Type of resource Ex: Aadhar,android,contact'
  /verification/rules/<rule_id>:
    parameters: []
    put:
      summary: Edit Rule
      operationId: put-verification-rules-rule_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Rule updated.
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      allowed:
                        type: integer
                      time:
                        type: integer
                      rule:
                        type: string
                      product_id:
                        type: string
                      resouce:
                        type: string
                      resource_id:
                        type: string
                      status:
                        type: string
                        x-stoplight:
                          id: 9omm5nrx5tmxg
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Rule updated.
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                      status: active
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
        '500':
          $ref: '#/components/responses/500'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                allowed:
                  type: integer
                time:
                  type: integer
                status:
                  type: string
              x-examples:
                Example 1:
                  allowed: 10
                  time: 3600
                  status: active/inactive
            examples:
              Example 1:
                value:
                  allowed: 10
                  time: 3600
                  status: active/inactive
      tags:
        - Verification
    patch:
      summary: Active/Inactive Rule
      operationId: patch-verification-rules-rule_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Rule updated.
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      allowed:
                        type: integer
                      time:
                        type: integer
                      rule:
                        type: string
                      product_id:
                        type: string
                      resouce:
                        type: string
                      resource_id:
                        type: string
                      status:
                        type: string
                        x-stoplight:
                          id: dmpgi8jasan53
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Rule updated.
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                      status: active
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
        '500':
          $ref: '#/components/responses/500'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  status: active
              properties:
                status:
                  type: string
              required:
                - status
            examples:
              Example 1:
                value:
                  status: active
      tags:
        - Verification
    delete:
      summary: Delete Rule
      operationId: delete-verification-rules-rule_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Rule deleted.
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Rule deleted.
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
        '500':
          $ref: '#/components/responses/500'
      description: |-
        Inactive Rule.

        This action will soft delete the rule (i.e. status = 0 in DB)
      tags:
        - Verification
    get:
      summary: Detail Rule
      operationId: get-verification-rules-rule_id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      allowed:
                        type: integer
                      time:
                        type: integer
                      rule:
                        type: string
                      product_id:
                        type: string
                      resouce:
                        type: string
                      resource_id:
                        type: string
                      status:
                        type: string
                        x-stoplight:
                          id: 5ajwwwjclaj9a
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      id: abc123
                      allowed: 0
                      time: 1379415
                      rule: max_allowed
                      product_id: heyoin
                      resouce: aadhar
                      resource_id: xyz123
                      status: active
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
              examples:
                Example 1:
                  value:
                    status: error
                    code: 404
                    message: Rule Not found
                    errors: {}
        '500':
          $ref: '#/components/responses/500'
      tags:
        - Verification
  '/verification/aadhaar/{product}/verify':
    get:
      summary: Aadhaar Limit Verification
      tags:
        - Verification
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      aadhaar_hash:
                        type: string
                      limit_exceeded:
                        type: boolean
                      reason:
                        type: array
                        items:
                          type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Aadhaar Verification
                    data:
                      aadhaar_hash: 695a6f9dbc4d1da853d9ab77c313624f
                      limit_exceeded: true
                      reason:
                        - max_allowed
                        - max_blacklisted
              examples:
                Both limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Aadhaar Verification
                    data:
                      aadhaar_hash: 695a6f9dbc4d1da853d9ab77c313624f
                      limit_exceeded: true
                      reason:
                        - max_allowed
                        - max_blacklisted
                max_allowed limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Aadhaar Verification
                    data:
                      aadhaar_hash: 695a6f9dbc4d1da853d9ab77c313624f
                      limit_exceeded: true
                      reason:
                        - max_allowed
                max_blacklisted limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Aadhaar Verification
                    data:
                      aadhaar_hash: 695a6f9dbc4d1da853d9ab77c313624f
                      limit_exceeded: true
                      reason:
                        - max_blacklisted
                No limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Aadhaar Verification
                    data:
                      aadhaar_hash: 695a6f9dbc4d1da853d9ab77c313624f
                      limit_exceeded: false
                      reason: []
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      aadhaar_no:
                        type: array
                        items:
                          type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      aadhaar_no:
                        - This field is required.
              examples:
                Example 1:
                  value:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      aadhaar_no:
                        - This field is required.
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
      operationId: get-verification-aadhaar
      parameters:
        - schema:
            type: string
          in: query
          name: aadhaar_no
          description: aadhaar card number
          required: true
      description: |
        Verify rules limit exceeded and return which fields have exceeded rule limit 
    parameters:
      - schema:
          type: string
        name: product
        in: path
        required: true
        description: 'Shortcode of product Ex: heyoin, myopin'
  '/billing_accounts/{id}':
    get:
      summary: Fetch Billing Account Details
      tags:
        - Billing Account
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Billing account details
                    data:
                      id: 63f358eaa8038982
                      ac_number: 7PVA5P
                      state_id: 18
                      gst_no: 18**********121
                      tan_no: 124sdkjh121
                      business_name: Some sd name pvt. ltd. newname2
                      business_address: '1176 Wayside Lane,Richmond, CA 948011 nesdsdw'
                      org_type_id: 102
                      billing_day: 20
                      parent_id: null
                      credit_limit: 10
                      min_bal: 10.02
                      recharge_on_min_bal: 10
                      auto_bill_email: 0
                      auto_bill_sms: 0
                      cr_limit_email: 0
                      cr_limit_sms: 0
                      status: 0
                      business_pan: **********
                      business_city: dabolim
                      business_state: goa
                      business_pincode: '403802'
                      business_country: India new
                      business_type: NA
                      billing_property: 3
                      account_manager_id: null
                      discount_id: null
                      applied_period: 0
                      total_pending: '0.000'
                      verification_state: 4
                      created: '2023-02-20T11:26:34Z'
                      modified: '2023-11-27T12:21:30Z'
                      org_type: 102
                      parent: null
                      account_manager: null
                      discount: null
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/BillingAccount'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Billing Account Details
                    data:
                      id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                      parent_id: null
                      ac_number: 4Z58SU
                      state_id: 0
                      gst_no: 18**********121
                      tan_no: null
                      uan: NA
                      business_name: VoiceTree Technologies Pvt Ltd
                      org_type_id: 10
                      billing_day: 1
                      credit_limit: 100
                      min_bal: 0
                      recharge_on_min_bal: 0
                      business_pan: **********
                      business_city: Delhi
                      business_state: New Delhi
                      business_pincode: '110006'
                      business_country: India
                      business_address: '345, 1st Floor'
                      business_type: NA
                      billing_property: 3
                      account_manager: 752bb6c2-68bd-49ca-9661-a8b1686f8151
                      applied_period: 0
                      total_pending: 0
                      verification_state: unverified
                      status: active
                      created: '2019-08-24T14:15:22Z'
                      modified: '2019-08-24T14:15:22Z'
                Child Billing Account Details:
                  value:
                    status: success
                    code: 200
                    message: Billing Account Details
                    data:
                      id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                      parent_id: 497f6eca-6276-4993-bfeb-53cbbbba6f07
                      ac_number: 4Z58SU
                      state_id: 0
                      gst_no: 18**********121
                      tan_no: null
                      uan: NA
                      business_name: VoiceTree Technologies Pvt Ltd
                      org_type_id: 10
                      billing_day: 1
                      credit_limit: 100
                      min_bal: 0
                      recharge_on_min_bal: 0
                      business_pan: **********
                      business_city: Delhi
                      business_state: New Delhi
                      business_pincode: '110006'
                      business_country: India
                      business_address: '345, 1st Floor'
                      business_type: NA
                      billing_property: 3
                      account_manager: 752bb6c2-68bd-49ca-9661-a8b1686f8151
                      applied_period: 0
                      total_pending: 0
                      verification_state: unverified
                      status: active
                      created: '2019-08-24T14:15:22Z'
                      modified: '2019-08-24T14:15:22Z'
            application/xml:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Billing account details
                    data:
                      id: 63f358eaa8038982
                      ac_number: 7PVA5P
                      state_id: 18
                      gst_no: 18**********121
                      tan_no: 124sdkjh121
                      business_name: Some sd name pvt. ltd. newname2
                      business_address: '1176 Wayside Lane,Richmond, CA 948011 nesdsdw'
                      org_type_id: 102
                      billing_day: 20
                      parent_id: null
                      credit_limit: 10
                      min_bal: 10.02
                      recharge_on_min_bal: 10
                      auto_bill_email: 0
                      auto_bill_sms: 0
                      cr_limit_email: 0
                      cr_limit_sms: 0
                      status: 0
                      business_pan: **********
                      business_city: dabolim
                      business_state: goa
                      business_pincode: '403802'
                      business_country: India new
                      business_type: NA
                      billing_property: 3
                      account_manager_id: null
                      discount_id: null
                      applied_period: 0
                      total_pending: '0.000'
                      verification_state: 4
                      created: '2023-02-20T11:26:34Z'
                      modified: '2023-11-27T12:21:30Z'
                      org_type: 102
                      parent: null
                      account_manager: null
                      discount: null
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: string
                      ac_number:
                        type: string
                      state_id:
                        type: integer
                      gst_no:
                        type: string
                      tan_no:
                        type: string
                      business_name:
                        type: string
                      business_address:
                        type: string
                      org_type_id:
                        type: integer
                      billing_day:
                        type: integer
                      parent_id:
                        type: string
                      credit_limit:
                        type: integer
                      min_bal:
                        type: number
                      recharge_on_min_bal:
                        type: integer
                      auto_bill_email:
                        type: integer
                      auto_bill_sms:
                        type: integer
                      cr_limit_email:
                        type: integer
                      cr_limit_sms:
                        type: integer
                      status:
                        type: integer
                      business_pan:
                        type: string
                      business_city:
                        type: string
                      business_state:
                        type: string
                      business_pincode:
                        type: string
                      business_country:
                        type: string
                      business_type:
                        type: string
                      billing_property:
                        type: integer
                      account_manager_id:
                        type: string
                      discount_id:
                        type: string
                      applied_period:
                        type: integer
                      total_pending:
                        type: string
                      verification_state:
                        type: integer
                      created:
                        type: string
                      modified:
                        type: string
                      org_type:
                        type: integer
              examples:
                Example 1:
                  value: |-
                    {
                        "status": "success",
                        "code": 200,
                        "message": "Billing account details",
                        "data": {
                            "id": "63f358eaa8038982",
                            "ac_number": "7PVA5P",
                            "state_id": 18,
                            "gst_no": "18**********121",
                            "tan_no": "124sdkjh121",
                            "business_name": "Some sd name pvt. ltd. newname2",
                            "business_address": "1176 Wayside Lane,Richmond, CA 948011 nesdsdw",
                            "org_type_id": 102,
                            "billing_day": 20,
                            "parent_id": null,
                            "credit_limit": 10,
                            "min_bal": 10.02,
                            "recharge_on_min_bal": 10.0,
                            "auto_bill_email": 0,
                            "auto_bill_sms": 0,
                            "cr_limit_email": 0,
                            "cr_limit_sms": 0,
                            "status": 0,
                            "business_pan": "**********",
                            "business_city": "dabolim",
                            "business_state": "goa",
                            "business_pincode": "403802",
                            "business_country": "India new",
                            "business_type": "NA",
                            "billing_property": 3,
                            "account_manager_id": null,
                            "discount_id": null,
                            "applied_period": 0,
                            "total_pending": "0.000",
                            "verification_state": 4,
                            "created": "2023-02-20T11:26:34Z",
                            "modified": "2023-11-27T12:21:30Z",
                            "org_type": 102,
                            
                        }
                    }
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Billing account not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Billing account not found
        '500':
          $ref: '#/components/responses/500'
      operationId: get-billing_accounts-ban_id
      x-stoplight:
        id: imfyjboycftze
      security:
        - Token: []
      parameters: []
      description: Fetch billing account details
    parameters:
      - schema:
          type: string
          format: uuid
        name: id
        in: path
        required: true
        description: Billing Account ID
    patch:
      summary: Patch Billing Account
      tags:
        - Billing Account
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    x-stoplight:
                      id: kgtbuasoy8dvx
                  code:
                    type: integer
                    x-stoplight:
                      id: w2snknh3tii76
                  message:
                    type: string
                    x-stoplight:
                      id: 3tou3g14b6o53
                  data:
                    type: object
                    x-stoplight:
                      id: a9ee0vs00v49f
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Billing Account Updated Successfully.
                    data: {}
        '400':
          $ref: '#/components/responses/400'
      operationId: patch-billing_accounts-id
      x-stoplight:
        id: rxvnvnkjc3e5i
      description: |-
        If the billing account has a registered GST Number, the `state_id`,`business_pan`,`business_address`,`business_city`, `business_state`,`business_country` fields cannot be updated via this API.

        Reason:
        These are derived from the GST Number and must remain consistent to ensure compliance with tax regulations.

        To update GST details:
        Use the following endpoint instead:

        ```
        POST /billing_account/{id}/gst
        ```

        Attempting to update any of these fields when GST is set will result in a validation error.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BillingAccount'
            examples:
              Update Billing Details:
                value:
                  state_id: 10
                  business_name: VoiceTree Technologies Pvt Ltd
                  business_city: Delhi
                  business_state: New Delhi
                  business_pincode: '110006'
                  business_country: India
                  business_address: '345, 1st Floor'
  '/billing_accounts/{id}/mark_fraud':
    post:
      summary: Mark account fraud
      operationId: post-billing_accounts
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Account marked as fraud.
              examples:
                Example 1:
                  value:
                    status: success
                    code: 200
                    message: Account marked as fraud.
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: Invalid user email
                    errors: {}
              examples:
                Invalid user email:
                  value:
                    status: error
                    code: ACC_0002
                    message: Invalid user email
                    errors: {}
                Already marked as fraud:
                  value:
                    status: error
                    code: ACC_0002
                    message: Account already marked as fraud
                    errors: {}
        '404':
          $ref: '#/components/responses/BillingAccount_NotFound'
      x-stoplight:
        id: g48qyt2pguzon
      description: Mark Billing Account as Fraud
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  user_email: <EMAIL>
                  reason: some comment
              properties:
                user_email:
                  type: string
                  format: email
                reason:
                  type: string
            examples:
              Example 1:
                value:
                  user_email: <EMAIL>
                  reason: some reason
      tags:
        - Billing Account
    parameters:
      - schema:
          type: string
          format: uuid
        name: id
        in: path
        required: true
        description: Billing Account ID
  '/billing_accounts/{id}/gst':
    post:
      summary: Update GST Number
      operationId: post-billing_accounts-ban_id-gst
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Gst number updated.
                    data:
                      gst_no: 18**********121
                      business_pan: **********
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    required:
                      - gst_no
                      - business_pan
                      - state_code
                    properties:
                      gst_no:
                        type: string
                      business_pan:
                        type: string
                      state_code:
                        type: string
                        x-stoplight:
                          id: 4i3tua1q23uzy
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: GST Number Updated.
                    data:
                      gst_no: 18**********121
                      business_pan: **********
                      state_code: '18'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties: {}
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors: {}
              examples:
                Invalid GST Number:
                  value:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      gst_no:
                        - Invalid GSTIN Number.
        '404':
          $ref: '#/components/responses/BillingAccount_NotFound'
      x-stoplight:
        id: qaw3nw6c1fr8p
      description: Update GST No. & Billing Details from GST API.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  gst_no: 18**********121
                  state_code: '18'
                  business_pan: **********
              required:
                - gst_no
              properties:
                gst_no:
                  type: string
                  example: 09AAACH7409R1ZZ
            examples:
              Success:
                value:
                  gst_no: 18**********121
      tags:
        - Billing Account
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
        description: Billing Account ID
  '/verification/uan/{product}/verify':
    parameters:
      - schema:
          type: string
        name: product
        in: path
        required: true
    get:
      summary: UAN limit verification
      tags:
        - Verification
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      limit_exceeded:
                        type: boolean
                      reason:
                        type: array
                        items:
                          type: string
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Gst Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_allowed
                        - max_blacklisted
              examples:
                Both limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Uan Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_allowed
                        - max_blacklisted
                max_allowed limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Uan Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_allowed
                max_blacklisted limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Uan Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_blacklisted
                No limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Uan Verification
                    data:
                      limit_exceeded: false
                      reason: []
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
      operationId: get-verification-uan-product-verify
      x-stoplight:
        id: 5361ppenzz47v
      description: |
        Verify rules limit exceeded and return which fields have exceeded rule limit
      parameters:
        - schema:
            type: string
          in: query
          name: uan
          description: uan number to verify
  '/verification/gst/{product}/verify':
    parameters:
      - schema:
          type: string
        name: product
        in: path
        required: true
    get:
      summary: GST limit verification
      tags:
        - Verification
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      gst_no:
                        type: string
                      limit_exceeded:
                        type: boolean
                      reason:
                        type: array
                        items:
                          type: object
                          properties: {}
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Gst Verification
                    data:
                      gst_no: '************45'
                      limit_exceeded: false
                      reason: []
              examples:
                Both limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Gst Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_allowed
                        - max_blacklisted
                max_allowed limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Gst Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_allowed
                max_blacklisted limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: Gst Verification
                    data:
                      limit_exceeded: true
                      reason:
                        - max_blacklisted
                No limit exceeded:
                  value:
                    status: success
                    code: 200
                    message: GST Verification
                    data:
                      limit_exceeded: false
                      reason: []
        '400':
          $ref: '#/components/responses/400'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
      operationId: get-verification-gst-product-verify
      x-stoplight:
        id: 2n9kl00ii4o9t
      parameters:
        - schema:
            type: string
          in: query
          name: gst_no
          description: gst number to verify
      description: |
        Verify rules limit exceeded and return which fields have exceeded rule limit
  /packages/:
    get:
      summary: Package List
      tags:
        - Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Package'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Packages
                    pagination:
                      count: 100
                      per_page: 10
                      total_pages: 10
                      current: 1
                    data:
                      - id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                        product_id: 0d012afa-f885-4e65-aeca-37e27701e2d1
                        package_category_id: 1
                        package_custom_id: 6025af1f-10cf-4a4c-844d-4cc90cb028ec
                        name: Heyo Lite Plan
                        code: X7WW9b
                        description: null
                        package_type: main
                        rent_per_month: '199.000'
                        renew_cycle: 1
                        is_public: false
                        ocs_flag: account
                        package_for: heyo
                        package_number: WW9b
                        discount: null
                        status: inactive
                        created: '2025-04-02T06:52:51Z'
      operationId: get-packages
      x-stoplight:
        id: iux4lf42vp0au
      description: API to fetch & filter Packages
      parameters:
        - schema:
            type: string
            example: '-created'
            enum:
              - created
              - rent_per_month
              - '-created'
              - '-rent_per_month'
            default: created
          in: query
          name: ordering
          description: 'Ordering, Use - for Descending'
        - schema:
            type: string
            enum:
              - active
              - inactive
          in: query
          name: status
          description: Filter by Status
        - schema:
            type: string
            example: tn54jn
          in: query
          name: code
          description: Filter by Package Code
        - $ref: '#/components/parameters/PaginationLimit'
        - $ref: '#/components/parameters/PaginationPage'
        - $ref: '#/components/parameters/FilterProductShortCode'
        - schema:
            type: string
            example: '199'
          in: query
          name: rent_per_month
          description: Filter by Rent Per Month
  '/packages/{id}':
    get:
      summary: Package Details
      tags:
        - Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    enum:
                      - success
                      - error
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Package'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Package Details
                    data:
                      id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                      product_id: 0d012afa-f885-4e65-aeca-37e27701e2d1
                      package_custom_id: 6025af1f-10cf-4a4c-844d-4cc90cb028ec
                      package_category_id: 29
                      name: Heyo Lite Plan
                      code: X7WW9b
                      description: null
                      package_type: main
                      rent_per_month: '199.000'
                      renew_cycle: 1
                      is_public: false
                      ocs_flag: account
                      package_for: heyo
                      package_number: WW9b
                      discount: null
                      status: active
                      created: '2025-04-02T06:52:51Z'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Not found.
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Not found.
      operationId: get-packages-detail
      x-stoplight:
        id: q2ei4cv2jw9hm
      parameters: []
      description: API to fetch package details by `package_id`
    parameters:
      - schema:
          type: string
          format: uuid
        name: id
        in: path
        required: true
        description: Package ID
  '/packages/{id}/features':
    get:
      summary: Package Features
      tags:
        - Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      - id: 2cac578f-671d-11e9-903b-027cf9c72897
                        product_feature_id: 5cc14375238b7180
                        product_feature_property_id: null
                        free_unit: 0
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: null
                        resource_key: ''
                        memcache_key: after_call_sms_custom
                        product_feature:
                          id: 5cc14375238b7180
                          product_id: 58ee66511dba5458
                          name: After call SMS (custom)
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: ''
                          memcache_key: after_call_sms_custom
                          type: 2
                          is_highlighted: 0
                          status: active
                          created: '2019-04-25T05:21:07Z'
                        created: '2019-04-25T05:34:12Z'
                        rate_slab: []
                      - id: 5a03ff3f88a63982
                        product_feature_id: 5981999c9ecdc669
                        product_feature_property_id: 5981999d506e5379
                        free_unit: 200
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: duration
                        memcache_key: call
                        product_feature:
                          id: 5981999c9ecdc669
                          product_id: 58ee66511dba5458
                          name: Incoming
                          unit: Minute(s)
                          billing_type: 'N'
                          resource_key: duration
                          memcache_key: ''
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-24T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 30d28acb-1399-40d3-80ef-75474eae710e
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 100000
                            rate: '2.150'
                            status: active
                            created: '2017-11-13T07:17:58Z'
                          - id: 5a03ff3f89323786
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 1000
                            rate: '1.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 5a03ff3f896bd546
                            package_feature_id: 5a03ff3f88a63982
                            min: 1001
                            max: 100000
                            rate: '2.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: c59851de-695f-42c5-aaca-521ae233be15
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 100000
                            rate: '2.150'
                            status: active
                            created: '2017-11-13T07:00:57Z'
                      - id: 5a03ff3f89a6a141
                        product_feature_id: 5981999d7107b400
                        product_feature_property_id: ''
                        free_unit: 100
                        additional: 0
                        rent_per_month: '0.000'
                        status: disabled
                        last_disabled_date: '2020-12-23T06:20:00Z'
                        resource_key: campaign_sms
                        memcache_key: re-marketing
                        product_feature:
                          id: 5981999d7107b400
                          product_id: 58ee66511dba5458
                          name: Remarketing SMS
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: campaign_sms
                          memcache_key: re-marketing
                          type: 1
                          is_highlighted: 0
                          status: inactive
                          created: '2015-01-15T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8a336844
                            package_feature_id: 5a03ff3f89a6a141
                            min: 0
                            max: 1000000
                            rate: '0.200'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 84cd3c65-c0a3-4612-a007-02858bb899ae
                            package_feature_id: 5a03ff3f89a6a141
                            min: 0
                            max: 1000000
                            rate: '0.200'
                            status: active
                            created: '2017-11-13T07:01:16Z'
                      - id: 5a03ff3f8a6dc503
                        product_feature_id: 5981999d24f5c646
                        product_feature_property_id: ''
                        free_unit: 3
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: ''
                        memcache_key: lines
                        product_feature:
                          id: 5981999d24f5c646
                          product_id: 58ee66511dba5458
                          name: Channel(s)
                          unit: Pair(s)
                          billing_type: L
                          resource_key: ''
                          memcache_key: lines
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-25T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8ab85405
                            package_feature_id: 5a03ff3f8a6dc503
                            min: 0
                            max: 10
                            rate: '1000.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 966effef-bd6a-4645-b044-87e455dce8aa
                            package_feature_id: 5a03ff3f8a6dc503
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:01:41Z'
                      - id: 5a03ff3f8b33d634
                        product_feature_id: 5981999d2fca2307
                        product_feature_property_id: ''
                        free_unit: 15
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: extensions
                        memcache_key: extension
                        product_feature:
                          id: 5981999d2fca2307
                          product_id: 58ee66511dba5458
                          name: User(s)
                          unit: User(s)
                          billing_type: L
                          resource_key: extensions
                          memcache_key: extension
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-25T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8b7bd882
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 100
                            rate: '0.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: c5afa6d7-b8ce-4d98-942b-0bfa84d18c7d
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:02:05Z'
                          - id: d417da8f-a0e7-4a47-914a-01e277af2127
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:20:20Z'
                      - id: 5a03ff3f8bf54697
                        product_feature_id: 5981999da7b82455
                        product_feature_property_id: ''
                        free_unit: 3
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: active_users
                        memcache_key: active_users
                        product_feature:
                          id: 5981999da7b82455
                          product_id: 58ee66511dba5458
                          name: Pro license(s)
                          unit: License(s)
                          billing_type: L
                          resource_key: active_users
                          memcache_key: active_users
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2015-11-06T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8c3fc806
                            package_feature_id: 5a03ff3f8bf54697
                            min: 0
                            max: 5
                            rate: '300.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: b4f3696a-5789-4d8d-9694-fa9e51071b74
                            package_feature_id: 5a03ff3f8bf54697
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:02:28Z'
                      - id: 5a03ff3f8c747547
                        product_feature_id: 5981999dc8512680
                        product_feature_property_id: ''
                        free_unit: 100
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: notification_sms
                        memcache_key: notification_sms
                        product_feature:
                          id: 5981999dc8512680
                          product_id: 58ee66511dba5458
                          name: After call SMS
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: notification_sms
                          memcache_key: notification_sms
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2015-12-18T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8d066117
                            package_feature_id: 5a03ff3f8c747547
                            min: 0
                            max: 100000
                            rate: '0.250'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 73eab30e-e2af-4c54-8c66-e832167f2f29
                            package_feature_id: 5a03ff3f8c747547
                            min: 0
                            max: 100000
                            rate: '0.300'
                            status: active
                            created: '2017-11-13T07:03:26Z'
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PackageFeature'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Package Features
                    data:
                      - id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                        product_feature_id: 65082e0f-b707-47bf-9c82-ed64d34d1aab
                        product_feature_property_id: null
                        free_unit: 10
                        additional: 0
                        rent_per_month: '199.000'
                        status: enabled
                        last_disabled_date: null
                        resource_key: ''
                        memcache_key: after_call_sms_custom
                        created: '2022-12-29T07:25:25Z'
                        product_feature:
                          id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                          product_id: 0d012afa-f885-4e65-aeca-37e27701e2d1
                          name: Incoming
                          unit: Minute(s)
                          billing_type: 'N'
                          resource_key: duration
                          memcache_key: ''
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2021-12-29T07:25:25Z'
                        rate_slabs:
                          - id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                            package_feature_id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                            min: 0
                            max: 10
                            rate: '1.000'
                            status: active
                            created: '2019-08-24T14:15:22Z'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Package features not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Package features not found
      operationId: get-packages-package_id-features
      x-stoplight:
        id: srpbblq2kzppg
      description: |
        Fetch features and its product feature detail and rate slabs of a package
      parameters:
        - schema:
            type: string
            enum:
              - created
            example: created
          in: query
          name: ordering
          description: 'Ordering, Use - for Descending'
        - schema:
            type: string
            example: chat
          in: query
          name: resource_key
          description: Filter By Resource Key
        - schema:
            type: string
            example: enabled
            enum:
              - enabled
              - disabled
              - suspended
          in: query
          name: status
          description: Filter by Status
    parameters:
      - schema:
          type: string
          example: 'chat_marketing,chat_utiltity,'
        in: query
        name: resource_key
        description: Comma separated values are allowed
      - schema:
          type: string
          format: uuid
        name: id
        in: path
        required: true
        description: Package ID
  /global_packages/:
    get:
      summary: Fetch Global Packages
      tags:
        - Global Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    pagination:
                      count: 21222
                      per_page: 10
                      total_pages: 2123
                      current: 1
                    data:
                      - id: 4505e1b0-3b09-43a7-a4e3-098246f601af
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Tollfree
                        package_type: main
                        rent_per_month: '45.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn54jn
                        status: active
                        ocs_flag: ocs
                        package_for: tollfree
                        package_number: 54jn
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2017-09-14T12:27:22Z'
                        discount: null
                      - id: 5619a059-0255-43b3-9c8e-54d10bd7a5c0
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Big Business Tollfree
                        package_type: main
                        rent_per_month: '5000.000'
                        renew_cycle: 12
                        is_public: true
                        code: tn7879
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: '7879'
                        description: null
                        created: '2017-09-23T10:15:05Z'
                        discount: null
                      - id: 58ee700daca3a800
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Mobile Tracking US
                        package_type: main
                        rent_per_month: '0.000'
                        renew_cycle: 1
                        is_public: true
                        code: mt00us
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 00us
                        description: 'Monitor and manage calls from mobile numbers of all agents, at no extra calling cost.'
                        created: '2014-09-21T18:30:00Z'
                        discount: null
                      - id: 58ee700daca3a801
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Tollfree US
                        package_type: main
                        rent_per_month: '99.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3kus
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3kus
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 58ee700daca3a802
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Premium US
                        package_type: main
                        rent_per_month: '1500.000'
                        renew_cycle: 1
                        is_public: true
                        code: vn3kus
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: virtual_number
                        package_number: 3kus
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2014-06-16T18:30:00Z'
                        discount: null
                      - id: 5985cbce3a1af760
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Premium
                        package_type: main
                        rent_per_month: '3000.000'
                        renew_cycle: 12
                        is_public: true
                        code: vn3kin
                        status: inactive
                        ocs_flag: ocs
                        package_for: virtual_number
                        package_number: 3kin
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2014-06-16T18:30:00Z'
                        discount: null
                      - id: 5985cbd17c394362
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Big Business
                        package_type: main
                        rent_per_month: '200.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3asd
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3asd
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 5985cbd17c394365
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Small Business
                        package_type: main
                        rent_per_month: '10.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3ksa
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3ksa
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 5985cbd24e326595
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Mobile Tracking
                        package_type: main
                        rent_per_month: '0.000'
                        renew_cycle: 12
                        is_public: true
                        code: mt00in
                        status: active
                        ocs_flag: ocs
                        package_for: mobile_tracking
                        package_number: 00in
                        description: 'Monitor and manage calls from mobile numbers of all agents, at no extra calling cost.'
                        created: '2014-09-21T18:30:00Z'
                        discount: null
                      - id: 5985cbd29a777389
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Premium
                        package_type: main
                        rent_per_month: '3000.000'
                        renew_cycle: 12
                        is_public: true
                        code: vn0000
                        status: active
                        ocs_flag: ocs
                        package_for: virtual_number
                        package_number: '0000'
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2016-07-28T18:30:00Z'
                        discount: null
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    $ref: '#/components/schemas/GlobalPackage'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Global Packages
                    pagination:
                      count: 21222
                      per_page: 10
                      total_pages: 2123
                      current: 1
                    data:
                      - id: 4505e1b0-3b09-43a7-a4e3-098246f601af
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Tollfree
                        package_type: main
                        rent_per_month: '45.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn54jn
                        status: active
                        ocs_flag: ocs
                        package_for: tollfree
                        package_number: 54jn
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2017-09-14T12:27:22Z'
                        discount: null
                      - id: 5619a059-0255-43b3-9c8e-54d10bd7a5c0
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Big Business Tollfree
                        package_type: main
                        rent_per_month: '5000.000'
                        renew_cycle: 12
                        is_public: true
                        code: tn7879
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: '7879'
                        description: null
                        created: '2017-09-23T10:15:05Z'
                        discount: null
                      - id: 58ee700daca3a800
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Mobile Tracking US
                        package_type: main
                        rent_per_month: '0.000'
                        renew_cycle: 1
                        is_public: true
                        code: mt00us
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: mobile_tracking
                        package_number: 00us
                        description: 'Monitor and manage calls from mobile numbers of all agents, at no extra calling cost.'
                        created: '2014-09-21T18:30:00Z'
                        discount: null
                      - id: 58ee700daca3a801
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Tollfree US
                        package_type: main
                        rent_per_month: '99.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3kus
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3kus
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 58ee700daca3a802
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Premium US
                        package_type: main
                        rent_per_month: '1500.000'
                        renew_cycle: 1
                        is_public: true
                        code: vn3kus
                        status: inactive
                        ocs_flag: myoperator_account
                        package_for: virtual_number
                        package_number: 3kus
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2014-06-16T18:30:00Z'
                        discount: null
                      - id: 5985cbce3a1af760
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Premium
                        package_type: main
                        rent_per_month: '3000.000'
                        renew_cycle: 12
                        is_public: true
                        code: vn3kin
                        status: inactive
                        ocs_flag: ocs
                        package_for: virtual_number
                        package_number: 3kin
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2014-06-16T18:30:00Z'
                        discount: null
                      - id: 5985cbd17c394362
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Big Business
                        package_type: main
                        rent_per_month: '200.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3asd
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3asd
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 5985cbd17c394365
                        product_id: 58ee700daca3a812
                        package_category_id: 2
                        name: Small Business
                        package_type: main
                        rent_per_month: '10.000'
                        renew_cycle: 1
                        is_public: true
                        code: tn3ksa
                        status: active
                        ocs_flag: myoperator_account
                        package_for: tollfree
                        package_number: 3ksa
                        description: 'Free of cost for the caller, owner has to bear these charges.'
                        created: '2014-08-08T18:30:00Z'
                        discount: null
                      - id: 5985cbd24e326595
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Mobile Tracking
                        package_type: main
                        rent_per_month: '0.000'
                        renew_cycle: 12
                        is_public: true
                        code: mt00in
                        status: active
                        ocs_flag: ocs
                        package_for: mobile_tracking
                        package_number: 00in
                        description: 'Monitor and manage calls from mobile numbers of all agents, at no extra calling cost.'
                        created: '2014-09-21T18:30:00Z'
                        discount: null
                      - id: 5985cbd29a777389
                        product_id: 58ee66511dba5458
                        package_category_id: 2
                        name: Premium
                        package_type: main
                        rent_per_month: '3000.000'
                        renew_cycle: 12
                        is_public: true
                        code: vn0000
                        status: active
                        ocs_flag: ocs
                        package_for: virtual_number
                        package_number: '0000'
                        description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                        created: '2016-07-28T18:30:00Z'
                        discount: null
      operationId: get-global_packages
      x-stoplight:
        id: sotwcdy2ee2tu
      description: API to fetch & filter Global Packages
      parameters:
        - schema:
            type: string
            enum:
              - active
              - inactive
          in: query
          name: status
          description: Filter by Package Status
        - schema:
            type: string
          in: query
          name: code
          description: Filter by Package Code
        - schema:
            type: string
            enum:
              - created
              - '-created'
              - rent_per_month
              - '-rent_per_month'
          in: query
          name: ordering
          description: 'Ordering, Use - for Descending, ex: -created'
        - $ref: '#/components/parameters/PaginationPage'
        - $ref: '#/components/parameters/PaginationLimit'
        - schema:
            type: string
            enum:
              - account
              - ocs
              - myoperator_account
              - none
          in: query
          name: ocs_flag
          description: Filter by OCS Flag
        - schema:
            type: string
            enum:
              - main
              - addon
          in: query
          name: package_type
          description: Filter by Package Type
        - schema:
            type: string
            enum:
              - tollfree
              - virtual_number
              - mobile_tracking
              - heyo
          in: query
          name: package_for
          description: Filter By Package For
        - schema:
            type: boolean
            enum:
              - 'true'
              - 'false'
          in: query
          name: is_public
          description: Filter By Is Public
        - $ref: '#/components/parameters/FilterPackageCategoryID'
        - $ref: '#/components/parameters/FilterProductShortCode'
        - $ref: '#/components/parameters/FIlterPackageCatergoryCode'
    parameters: []
  '/global_packages/{id}':
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
    get:
      summary: Global Package Details
      tags:
        - Global Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    x-stoplight:
                      id: svdkstjht2vrq
                  code:
                    type: integer
                    x-stoplight:
                      id: 3483c3ccgol4p
                  message:
                    type: string
                    x-stoplight:
                      id: o4k02tu7fvvba
                  data:
                    $ref: '#/components/schemas/GlobalPackage'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Global Package Detail
                    data:
                      id: 497f6eca-6276-4993-bfeb-53cbbbba6f08
                      product_id: 0d012afa-f885-4e65-aeca-37e27701e2d1
                      package_category_id: 29
                      name: Heyo Lite Plan
                      code: AWZGBH
                      description: null
                      package_type: main
                      rent_per_month: '199.000'
                      renew_cycle: 1
                      is_public: true
                      ocs_flag: myoperator_account
                      package_for: heyo
                      package_number: '0012'
                      discount: null
                      status: active
                      created: '2024-01-18T07:25:21Z'
        '404':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    x-stoplight:
                      id: pt1hbkxf2ojlk
                  code:
                    type: integer
                    x-stoplight:
                      id: ylhytyq1y6dnv
                  message:
                    type: string
                    x-stoplight:
                      id: b3d833heuaqya
                  error:
                    type: object
                    x-stoplight:
                      id: 66lp9ej6jv1nx
              examples:
                Not Found:
                  value:
                    status: error
                    code: 404
                    message: Global Package Not Found
                    error: {}
      operationId: get-global_packages-id
      x-stoplight:
        id: tsl2dfa1qfma7
      x-internal: false
  '/global_packages/{id}/features':
    get:
      summary: Global Package Features
      tags:
        - Global Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      - id: 5a03ff3f8b33d634
                        product_feature_id: 5981999d2fca2307
                        product_feature_property_id: ''
                        free_unit: 15
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: extensions
                        memcache_key: extension
                        product_feature:
                          id: 5981999d2fca2307
                          product_id: 58ee66511dba5458
                          name: User(s)
                          unit: User(s)
                          billing_type: L
                          resource_key: extensions
                          memcache_key: extension
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-25T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8b7bd882
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 100
                            rate: '0.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: c5afa6d7-b8ce-4d98-942b-0bfa84d18c7d
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:02:05Z'
                          - id: d417da8f-a0e7-4a47-914a-01e277af2127
                            package_feature_id: 5a03ff3f8b33d634
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:20:20Z'
                      - id: 65e6d1e2b1f91821
                        product_feature_id: 61cc13732d9cb643
                        product_feature_property_id: ''
                        free_unit: 5
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: null
                        resource_key: departments
                        memcache_key: departments
                        product_feature:
                          id: 61cc13732d9cb643
                          product_id: 61cab45237321971
                          name: Department(s)
                          unit: Department(s)
                          billing_type: L
                          resource_key: departments
                          memcache_key: departments
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2021-12-29T07:25:25Z'
                        created: '2024-03-05T08:03:46Z'
                        rate_slab:
                          - id: 65e6d1e2bf6d8802
                            package_feature_id: 65e6d1e2b1f91821
                            min: 0
                            max: 100000
                            rate: '300.000'
                            status: active
                            created: '2024-03-05T08:03:46Z'
                      - id: 65e6d1e24db40592
                        product_feature_id: 65dd6eda127e8876
                        product_feature_property_id: ''
                        free_unit: 0
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: null
                        resource_key: ''
                        memcache_key: chat
                        product_feature:
                          id: 65dd6eda127e8876
                          product_id: 61cab45237321971
                          name: Chat
                          unit: Unit(s)
                          billing_type: 'N'
                          resource_key: ''
                          memcache_key: chat
                          type: 2
                          is_highlighted: 0
                          status: active
                          created: '2024-02-27T05:11:53Z'
                        created: '2024-03-05T08:03:46Z'
                        rate_slab:
                          - id: 65e6d1e25a76e760
                            package_feature_id: 65e6d1e24db40592
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2024-03-05T08:03:46Z'
                      - id: 2cac578f-671d-11e9-903b-027cf9c72897
                        product_feature_id: 5cc14375238b7180
                        product_feature_property_id: null
                        free_unit: 0
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: null
                        resource_key: ''
                        memcache_key: after_call_sms_custom
                        product_feature:
                          id: 5cc14375238b7180
                          product_id: 58ee66511dba5458
                          name: After call SMS (custom)
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: ''
                          memcache_key: after_call_sms_custom
                          type: 2
                          is_highlighted: 0
                          status: active
                          created: '2019-04-25T05:21:07Z'
                        created: '2019-04-25T05:34:12Z'
                        rate_slab: []
                      - id: 5a03ff3f88a63982
                        product_feature_id: 5981999c9ecdc669
                        product_feature_property_id: 5981999d506e5379
                        free_unit: 200
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: duration
                        memcache_key: call
                        product_feature:
                          id: 5981999c9ecdc669
                          product_id: 58ee66511dba5458
                          name: Incoming
                          unit: Minute(s)
                          billing_type: 'N'
                          resource_key: duration
                          memcache_key: ''
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-24T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 30d28acb-1399-40d3-80ef-75474eae710e
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 100000
                            rate: '2.150'
                            status: active
                            created: '2017-11-13T07:17:58Z'
                          - id: 5a03ff3f89323786
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 1000
                            rate: '1.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 5a03ff3f896bd546
                            package_feature_id: 5a03ff3f88a63982
                            min: 1001
                            max: 100000
                            rate: '2.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: c59851de-695f-42c5-aaca-521ae233be15
                            package_feature_id: 5a03ff3f88a63982
                            min: 0
                            max: 100000
                            rate: '2.150'
                            status: active
                            created: '2017-11-13T07:00:57Z'
                      - id: 5a03ff3f89a6a141
                        product_feature_id: 5981999d7107b400
                        product_feature_property_id: ''
                        free_unit: 100
                        additional: 0
                        rent_per_month: '0.000'
                        status: disabled
                        last_disabled_date: '2020-12-23T06:20:00Z'
                        resource_key: campaign_sms
                        memcache_key: re-marketing
                        product_feature:
                          id: 5981999d7107b400
                          product_id: 58ee66511dba5458
                          name: Remarketing SMS
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: campaign_sms
                          memcache_key: re-marketing
                          type: 1
                          is_highlighted: 0
                          status: inactive
                          created: '2015-01-15T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8a336844
                            package_feature_id: 5a03ff3f89a6a141
                            min: 0
                            max: 1000000
                            rate: '0.200'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 84cd3c65-c0a3-4612-a007-02858bb899ae
                            package_feature_id: 5a03ff3f89a6a141
                            min: 0
                            max: 1000000
                            rate: '0.200'
                            status: active
                            created: '2017-11-13T07:01:16Z'
                      - id: 5a03ff3f8a6dc503
                        product_feature_id: 5981999d24f5c646
                        product_feature_property_id: ''
                        free_unit: 3
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: ''
                        memcache_key: lines
                        product_feature:
                          id: 5981999d24f5c646
                          product_id: 58ee66511dba5458
                          name: Channel(s)
                          unit: Pair(s)
                          billing_type: L
                          resource_key: ''
                          memcache_key: lines
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2013-09-25T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8ab85405
                            package_feature_id: 5a03ff3f8a6dc503
                            min: 0
                            max: 10
                            rate: '1000.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 966effef-bd6a-4645-b044-87e455dce8aa
                            package_feature_id: 5a03ff3f8a6dc503
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:01:41Z'
                      - id: 5a03ff3f8bf54697
                        product_feature_id: 5981999da7b82455
                        product_feature_property_id: ''
                        free_unit: 3
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: active_users
                        memcache_key: active_users
                        product_feature:
                          id: 5981999da7b82455
                          product_id: 58ee66511dba5458
                          name: Pro license(s)
                          unit: License(s)
                          billing_type: L
                          resource_key: active_users
                          memcache_key: active_users
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2015-11-06T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8c3fc806
                            package_feature_id: 5a03ff3f8bf54697
                            min: 0
                            max: 5
                            rate: '300.000'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: b4f3696a-5789-4d8d-9694-fa9e51071b74
                            package_feature_id: 5a03ff3f8bf54697
                            min: 0
                            max: 1000000
                            rate: '1.000'
                            status: active
                            created: '2017-11-13T07:02:28Z'
                      - id: 5a03ff3f8c747547
                        product_feature_id: 5981999dc8512680
                        product_feature_property_id: ''
                        free_unit: 100
                        additional: 0
                        rent_per_month: '0.000'
                        status: enabled
                        last_disabled_date: '2017-11-12T04:00:16Z'
                        resource_key: notification_sms
                        memcache_key: notification_sms
                        product_feature:
                          id: 5981999dc8512680
                          product_id: 58ee66511dba5458
                          name: After call SMS
                          unit: SMS(s)
                          billing_type: 'N'
                          resource_key: notification_sms
                          memcache_key: notification_sms
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2015-12-18T00:00:00Z'
                        created: '2017-11-09T07:09:51Z'
                        rate_slab:
                          - id: 5a03ff3f8d066117
                            package_feature_id: 5a03ff3f8c747547
                            min: 0
                            max: 100000
                            rate: '0.250'
                            status: active
                            created: '2017-11-09T07:09:51Z'
                          - id: 73eab30e-e2af-4c54-8c66-e832167f2f29
                            package_feature_id: 5a03ff3f8c747547
                            min: 0
                            max: 100000
                            rate: '0.300'
                            status: active
                            created: '2017-11-13T07:03:26Z'
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/GlobalPackageFeature'
              examples:
                Only Package Feature:
                  value:
                    status: success
                    code: 200
                    message: Global Package Features
                    data:
                      - id: 1ed3900b-3aaf-4c78-ba68-7fbe55b06d4a
                        product_feature_id: 1ed3900b-3aaf-4c78-ba68-7fbe55b06d43
                        product_feature_property_id: 1ed3900b-3aaf-4c78-ba68-7fbe55b06d4b
                        free_unit: 0
                        rent_per_month: '0.000'
                        last_disabled_date: null
                        resource_key: ''
                        memcache_key: after_call_sms_custom
                        status: enabled
                        created: '2024-01-18T07:25:21Z'
                        modified: '2024-01-18T07:25:21Z'
                With Expand - Product Feature:
                  value:
                    status: success
                    code: 200
                    message: Global Package Features
                    data:
                      - id: 640c10b7-60bc-4f47-b606-0c294609614f
                        product_feature_id: 640c10b7-60bc-4f47-b606-0c294609614s
                        product_feature_property_id: null
                        free_unit: 1
                        rent_per_month: '0.000'
                        last_disabled_date: null
                        resource_key: fix_did
                        memcache_key: fix_did
                        status: enabled
                        created: '2024-01-18T07:25:21Z'
                        modified: '2024-01-18T07:25:21Z'
                        product_feature:
                          id: 61cc13bc8a04a961
                          product_id: 61cab45237321971
                          name: Fix did(s)
                          unit: DID(s)
                          billing_type: L
                          resource_key: fix_did
                          memcache_key: fix_did
                          type: 1
                          is_highlighted: 0
                          status: active
                          created: '2021-12-29T07:25:25Z'
                With Expand - Rate Slabs:
                  value:
                    status: success
                    code: 200
                    message: Global Package Features
                    data:
                      - id: 6509a512-6255-40b8-9d90-71f0e68d8292
                        product_feature_id: 6509a512-6255-40b8-9d90-71f0e68d8293
                        product_feature_property_id: ''
                        free_unit: 1
                        rent_per_month: '0.000'
                        last_disabled_date: null
                        status: enabled
                        resource_key: 'fix_did'
                        memcache_key: fix_did
                        created: '2024-01-18T07:25:21Z'
                        modified: '2024-04-10T05:45:36Z'
                        rate_slabs:
                          - id: 0740bfa8-13ca-49ee-aa1f-f8db6327d2dd
                            package_feature_id: 6509a512-6255-40b8-9d90-71f0e68d8292
                            min: 0
                            max: 1000000
                            rate: '150.000'
                            status: active
                            created: '2024-01-18T07:25:21Z'
                            modified: '2024-01-18T07:25:21Z'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Package features not found
              examples:
                Not Found:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Global Package not found
      operationId: get-global_-packages-package_id-features
      x-stoplight:
        id: tpykrcfm1d8il
      description: 'API to fetch Global Package Features, Returns all the features without pagination.'
      parameters:
        - schema:
            type: string
            example: 'expand=product_feature,rate_slabs'
          in: query
          name: expand
          description: Comma-separated list of expandable fields
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
        description: Global Package ID
  /global_packages/categories:
    get:
      summary: Global Package Categories
      tags:
        - Global Packages
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    x-stoplight:
                      id: itfx7q56luujy
                  code:
                    type: integer
                    x-stoplight:
                      id: f0utnmct3hw5z
                  message:
                    type: string
                    x-stoplight:
                      id: 92ykn5igkx0kc
                  data:
                    type: array
                    x-stoplight:
                      id: ig23qcq9tnmks
                    items:
                      $ref: '#/components/schemas/PackageCategory'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Pacakge Categories
                    data:
                      - id: 1
                        product_id: 0d012afa-f885-4e65-aeca-37e27701e2d1
                        code: L
                        name: Office Contact System
                        description: null
                        weightage: 1
                        created: '2019-08-24T14:15:22Z'
                        modified: '2019-08-24T14:15:22Z'
      operationId: get-packages-categories
      x-stoplight:
        id: pjqr97enb1chf
      parameters:
        - $ref: '#/components/parameters/FilterProductShortCode'
        - schema:
            type: string
            enum:
              - L
              - D
          in: query
          name: code
          description: Filter by Category Code
        - schema:
            type: string
            example: '-weightage'
            enum:
              - weightage
              - '-weightage'
            default: weightage
          in: query
          name: ordering
          description: Use - to order by Descending
    parameters:
      - $ref: '#/components/parameters/FilterProductShortCode'
  /services/:
    get:
      summary: Fetch Services
      tags:
        - Services
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Service'
              examples:
                Services Data:
                  value:
                    status: success
                    code: '200'
                    message: Services
                    pagination:
                      count: 1
                      per_page: 1
                      total_pages: 1
                      current: 1
                    data:
                      - id: 67f658db91975979
                        product_id: 58ee66511dba5458
                        billing_account_id: 67f658db90ff4753
                        gsn: 67f658db7b608629
                        user_profile_id: null
                        country_id: 99
                        timezone: Asia/Kolkata
                        rent_per_month: 199
                        renew_cycle: 1
                        activation_date: '2019-08-24T14:15:22Z'
                        expiry_date: '2019-09-24T14:15:22Z'
                        last_renewal: null
                        live_status: demo
                        status: active
                        churn_date: null
                        current_usages: 0
                        created: '2019-08-24T14:15:22Z'
                        modified: '2019-08-24T14:15:22Z'
                Services With Expand Billing Account:
                  value:
                    status: success
                    code: '200'
                    message: Services
                    pagination:
                      count: 1
                      per_page: 1
                      total_pages: 1
                      current: 1
                    data:
                      - id: 67f658db91975979
                        product_id: 58ee66511dba5458
                        billing_account_id: 67f658db90ff4753
                        gsn: 67f658db7b608629
                        user_profile_id: null
                        country_id: 99
                        timezone: Asia/Kolkata
                        rent_per_month: 199
                        renew_cycle: 1
                        activation_date: '2019-08-24T14:15:22Z'
                        expiry_date: '2019-09-24T14:15:22Z'
                        last_renewal: null
                        live_status: demo
                        status: active
                        churn_date: null
                        current_usages: 0
                        created: '2019-08-24T14:15:22Z'
                        modified: '2019-08-24T14:15:22Z'
                        billing_account:
                          id: 67f658db90ff4753
                          parent_id: null
                          ac_number: L47G8G
                          state_id: 27
                          gst_no: NA
                          tan_no: NA
                          uan: NA
                          business_name: ABC Private Limited
                          org_type_id: 109
                          billing_day: 9
                          credit_limit: 100
                          min_bal: 10
                          recharge_on_min_bal: 1000
                          business_pan: NA
                          business_city: NA
                          business_state: NA
                          business_pincode: NA
                          business_country: India
                          business_address: NA
                          business_type: NA
                          billing_property: 3
                          account_manager: null
                          applied_period: 0
                          total_pending: 0
                          verification_state: unverified
                          status: active
                          created: '2019-08-24T14:15:22Z'
                          modified: '2019-08-24T14:15:22Z'
      operationId: get-services
      x-stoplight:
        id: d5ua1c54n96tn
      parameters:
        - $ref: '#/components/parameters/Authorization'
        - $ref: '#/components/parameters/PaginationLimit'
        - schema:
            type: string
            format: date-time
            example: '2025-04-09T11:00:00Z'
          in: query
          name: activation_date__gte
          description: Filter Activation Date Greater Than
        - schema:
            type: string
            example: 'active,inactive,suspended'
          in: query
          name: status
          description: Filter Status (Comma Separated)
        - schema:
            type: string
            example: 'demo,prepaid,postpaid'
          in: query
          name: live_status
          description: Filter Live Status (Comma Separated )
        - schema:
            type: string
            enum:
              - activation_date
              - created
            example: activation_date
          in: query
          name: ordering
          description: 'Use - to order by DESC, ex: -activation_date'
        - schema:
            type: string
            enum:
              - billing_account
            example: billing_account
          in: query
          name: expand
          description: Expand (Comma Separated)
        - $ref: '#/components/parameters/PaginationPage'
      requestBody:
        content: {}
  '/services/{gsn}/packages':
    get:
      summary: Service package
      tags:
        - Services
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: ''
                    data:
                      current:
                        id: 65c21f7aedf9d139
                        start_time: '2024-02-05T18:30:00Z'
                        end_time: '2024-08-06T18:29:59Z'
                        name: Growing Business
                        package_type: main
                        rent_per_month: '1300.0'
                        renew_cycle: '6'
                        package_status: inactive
                      upcoming:
                        id: 65c21137cb9b8115
                        start_time: '2024-08-06T11:05:26Z'
                        end_time: '2024-02-05T18:29:59Z'
                        name: Lite- 1C
                        package_type: main
                        rent_per_month: '1150.0'
                        renew_cycle: '12'
                        package_status: inactive
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      current:
                        type: object
                        properties:
                          id:
                            type: string
                          start_time:
                            type: string
                          end_time:
                            type: string
                          name:
                            type: string
                          package_type:
                            type: string
                          rent_per_month:
                            type: string
                          renew_cycle:
                            type: string
                          package_id:
                            type: string
                            x-stoplight:
                              id: 65894qx7fdc1g
                          service_id:
                            type: string
                            x-stoplight:
                              id: hg9pa4au0zh60
                      upcoming:
                        type: object
                        properties:
                          id:
                            type: string
                          start_time:
                            type: string
                          end_time:
                            type: string
                          name:
                            type: string
                          package_type:
                            type: string
                          rent_per_month:
                            type: string
                          renew_cycle:
                            type: string
                          package_id:
                            type: string
                            x-stoplight:
                              id: 241v7ixmfrvwx
                          service_id:
                            type: string
                            x-stoplight:
                              id: q3sk1j85keo3l
              examples:
                Current and upcoming package present:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      current:
                        id: 65c21f7aedf9d139
                        package_id: 65d5da94dc2d0947
                        service_id: 65bb843038d39883
                        start_time: '2024-02-05T18:30:00Z'
                        end_time: '2024-08-06T18:29:59Z'
                        name: Growing Business
                        package_type: main
                        rent_per_month: '1300.0'
                        renew_cycle: '6'
                      upcoming:
                        id: 65c21137cb9b8115
                        package_id: 65d5da94dc2d0937
                        service_id: 65bb843038d39884
                        start_time: '2024-08-06T11:05:26Z'
                        end_time: '2024-02-05T18:29:59Z'
                        name: Lite- 1C
                        package_type: main
                        rent_per_month: '1150.0'
                        renew_cycle: '12'
                No upcoming package:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      current:
                        id: 65c0bc0fb1543904
                        start_time: '2024-01-31T18:30:00Z'
                        end_time: '2024-05-01T18:29:59Z'
                        name: Standard (Monthly)
                        package_type: main
                        rent_per_month: '499.0'
                        renew_cycle: '3'
                      upcoming: null
                Both not present:
                  value:
                    status: success
                    code: 200
                    message: ''
                    data:
                      current: null
                      upcoming: null
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
        '500':
          $ref: '#/components/responses/500'
      operationId: get-services-service_id-package
      x-stoplight:
        id: uivcyo6p529k2
      description: Get current and upcoming packages
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
        description: Company ID
  '/services/<gsn>/package_suggestion/{gsn}':
    get:
      summary: Package Suggestion
      tags:
        - Services
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string
                  code:
                    type: string
                  data:
                    type: object
                    properties:
                      packages:
                        type: object
                        properties:
                          higher:
                            type: array
                            items:
                              type: object
                              properties:
                                id:
                                  type: string
                                product_id:
                                  type: string
                                name:
                                  type: string
                                package_type:
                                  type: string
                                rent_per_month:
                                  type: string
                                renew_cycle:
                                  type: string
                                is_public:
                                  type: string
                                code:
                                  type: string
                                status:
                                  type: string
                                ocs_flag:
                                  type: string
                                package_for:
                                  type: string
                                package_category_id:
                                  type: string
                                package_number:
                                  type: string
                                discount_id:
                                  type: string
                                description:
                                  type: string
                                created:
                                  type: string
                                modified:
                                  type: string
                          lower:
                            type: string
                x-examples:
                  Example 1:
                    status: success
                    message: higher package suggestion list
                    code: '200'
                    data:
                      packages:
                        higher:
                          - id: 6db6195c-ca82-43d7-a6c7-7ba4c4699a85
                            product_id: 61cab45237321971
                            name: Standard Plan
                            package_type: main
                            rent_per_month: '499.000'
                            renew_cycle: '1'
                            is_public: true
                            code: STT5FJ
                            status: '1'
                            ocs_flag: myoperator_account
                            package_for: heyo
                            package_category_id: '29'
                            package_number: '0011'
                            discount_id: null
                            description: 'A virtual number that can be tracked for call management, callers are charged normally.'
                            created: '2021-12-30 07:53:27'
                            modified: '2024-02-27 10:08:35'
                        lower: null
        '403':
          $ref: '#/components/responses/403'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: gsn not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: gsn not found
        '500':
          $ref: '#/components/responses/500'
      operationId: get-services-gsn-package_suggestion
      x-stoplight:
        id: jg3x8l7o7vnf4
      description: Api to get package suggestions based on gsn
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
        description: Company ID
  '/services/{gsn}/pending_usage':
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
        description: Company ID
    get:
      summary: Pending Usage
      tags:
        - Services
      responses:
        2XX:
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      pending_rental:
                        type: integer
                      pending_usages:
                        type: integer
                      current_usages:
                        type: integer
                      other_charges:
                        type: integer
                      service_number_cost:
                        type: integer
                      discount:
                        type: integer
                      advance:
                        type: integer
                      available:
                        type: integer
                      currency:
                        type: string
                x-examples:
                  Example 1:
                    status: success
                    code: '200'
                    message: Pending Usage
                    data:
                      pending_rental: 0
                      pending_usages: 0
                      current_usages: 100
                      other_charges: 0
                      service_number_cost: 0
                      discount: 0
                      advance: 200
                      available: 100
                      currency: INR
              examples:
                Positive Balance:
                  value:
                    status: success
                    code: '200'
                    message: Pending Usage
                    data:
                      pending_rental: 0
                      pending_usages: 0
                      current_usages: 100
                      other_charges: 0
                      service_number_cost: 0
                      discount: 0
                      advance: 200
                      available: 100
                      currency: INR
                Negative Balance:
                  value:
                    status: success
                    code: '200'
                    message: Pending Usage
                    data:
                      pending_rental: 199
                      pending_usages: 0
                      current_usages: 10
                      other_charges: 0
                      service_number_cost: 0
                      discount: 0
                      advance: 0
                      available: -209
                      currency: INR
      operationId: get-services-gsn-billing
      x-stoplight:
        id: aiusp5i40ay9y
  '/services/{gsn}/deduct_balance':
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
    post:
      summary: Deduct Balance
      tags:
        - Services
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties: {}
              examples:
                Success:
                  value:
                    status: success
                    code: '200'
                    message: Balance Deducted
                    data: {}
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties: {}
              examples:
                Insufficient Balance:
                  value:
                    status: error
                    code: ACC_4010
                    message: Insufficient Balance
                    errors: {}
      operationId: post-services-gsn-deduct_balance
      x-stoplight:
        id: eib0om5807hio
      description: This is a temporary api till event based billing is introduced in accounts
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  resouce: campaign
                  resource_id: '123332'
                  deduct_amount: 500
              required:
                - resouce
                - resource_id
                - deduct_amount
              properties:
                resouce:
                  type: string
                resource_id:
                  type: string
                deduct_amount:
                  type: number
            examples:
              Success:
                value:
                  resouce: campaign
                  resource_id: '123332'
                  deduct_amount: 500
  '/services/{gsn}/activation_payable_amount':
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
    post:
      summary: Fetch Payable Amount
      operationId: post-services-gsn-activation_amount
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      rental_amount:
                        type: number
                        x-stoplight:
                          id: s47yz5opr4zvs
                        format: float
                        example: 199
                        minimum: 1
                      number_cost:
                        type: number
                        x-stoplight:
                          id: r69bti7k4op73
                        format: float
                        default: 0
                        example: 0
                        minimum: 0
                      additional_cost:
                        x-stoplight:
                          id: nkrbahd61lp7f
                        type: number
                        format: float
                        example: 0
                        default: 0
                        minimum: 0
                      discount:
                        type: number
                        x-stoplight:
                          id: a1ifrkmm3o3hp
                        format: float
                        default: null
                        nullable: true
                      tax_details:
                        type: array
                        x-stoplight:
                          id: q5yavsraaldi1
                        items:
                          $ref: '#/components/schemas/TaxDetails'
                      tax_total:
                        type: number
                        x-stoplight:
                          id: 1oo15d6zcbcci
                        format: float
                        example: 33.82
                      amount_without_tax:
                        type: number
                        x-stoplight:
                          id: cluanvrffefju
                        format: float
                        example: 199
                      payable_amount:
                        type: number
                        x-stoplight:
                          id: xhsplsg2yiqp6
                        format: float
                        example: 232.84
                        minimum: 0
                      currency:
                        x-stoplight:
                          id: o4npg1xyh0yot
                        enum:
                          - INR
                          - USD
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: Payable Amount
                    data:
                      rental_amount: 199
                      number_cost: 0
                      additional_cost: 0
                      discount: null
                      tax_details:
                        - name: GST
                          tax_percent: 18
                          amount: 33.82
                      tax_total: 33.82
                      amount_without_tax: 199
                      payable_amount: 232.82
                      currency: INR
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      package_id:
                        type: array
                        items:
                          type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      package_id:
                        - This field is required.
              examples:
                Validation error:
                  value:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      package_id:
                        - This field is required.
                Service already running:
                  value:
                    status: error
                    code: ACC_0002
                    message: Service is already running on the given service number
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
      x-stoplight:
        id: 1w97ger62fn8b
      description: |-
        Fetches the total amount to be paid for activating a service, including package cost, number cost, additional cost, applicable taxes, and discounts (if any).

        This API helps clients display accurate pricing information before initiating payment.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  package_id: c490edcf-cae2-4493-b9e4-f14b0eead60e
                  service_number: '************'
                  custom_amount: 500
                  redirect_url: 'https://us.app.myoperator.dev/cafs/paid/normal/2'
                  name: Shenoy
                  email: <EMAIL>
                  phone: '**********'
              required:
                - package_id
                - service_number
              properties:
                package_id:
                  type: string
                  format: uuid
                  description: This can be either a Global Package ID or a Custom Package ID.
                service_number:
                  type: string
                  x-stoplight:
                    id: 6x9tpkr4umty6
                  description: 'The number which customer has selected for activation, It can be dummy number is case of Heyo & MyOperator (Non Calling) accounts.'
                  example: '************'
            examples:
              Success:
                value:
                  package_id: c490edcf-cae2-4493-b9e4-f14b0eead60e
                  service_number: '************'
      tags:
        - Service Activation
  '/services/{gsn}/initiate_activation':
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
    post:
      summary: Initiate Activation
      operationId: post-services-service_id-initiate_activation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: Activation Link Created
                    data:
                      url: 'https://accounts.myoperator.dev/payment/WmpoallqTTRZakJsTWpJek9HRm1Nek5sWlRjME9HWXdZV1l6WkdZeFptUmlNekpoWWpSaVkyTTJNakZrTnpnME9UVTNNR1UwWldNMlpqUm1PV1pqTTJ5RjEyVkpwcnFFdFdQL09uR205VE0rbU1ZWU9oN3N4RW1nczI5a2RGLzVEdE8vNkhBdFFucnFrRUtkWXg4K3B0ekZxbVVleVZ1M2dmQmQxZGFNTmNjMHEyNFY1Ly9mZFRpRERUOGk5eGN5aTZvL204eWZRb0QxN1ZvbjNCTTA3dz09'
                      payment_id: 16932815083408gPAk
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      url:
                        type: string
                        nullable: true
                      payment_id:
                        type: string
                        nullable: true
                      activation_status:
                        x-stoplight:
                          id: nkrbahd61lp7f
                        enum:
                          - pending
                          - activated
                        readOnly: true
              examples:
                Activation Link:
                  value:
                    status: success
                    code: 200
                    message: Activation Link Created
                    data:
                      url: 'https://accounts.myoperator.dev/payment/WmpoallqTTRZakJsTWpJek9HRm1Nek5sWlRjME9HWXdZV1l6WkdZeFptUmlNekpoWWpSaVkyTTJNakZrTnpnME9UVTNNR1UwWldNMlpqUm1PV1pqTTJ5RjEyVkpwcnFFdFdQL09uR205VE0rbU1ZWU9oN3N4RW1nczI5a2RGLzVEdE8vNkhBdFFucnFrRUtkWXg4K3B0ekZxbVVleVZ1M2dmQmQxZGFNTmNjMHEyNFY1Ly9mZFRpRERUOGk5eGN5aTZvL204eWZRb0QxN1ZvbjNCTTA3dz09'
                      payment_id: 16932815083408gPAk
                      activation_status: pending
                Amount already Paid:
                  value:
                    status: success
                    code: 200
                    message: Service Activated
                    data:
                      url: 'null'
                      payment_id: null
                      activation_status: activated
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      package_id:
                        type: array
                        items:
                          type: string
                x-examples:
                  Example 1:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      package_id:
                        - This field is required.
              examples:
                Validation error:
                  value:
                    status: error
                    code: ACC_0002
                    message: 'Validation Failed, Please check errors field for details'
                    errors:
                      package_id:
                        - This field is required.
                Service already running:
                  value:
                    status: error
                    code: ACC_0002
                    message: Service is already running on the given service number
                    errors: {}
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
              examples:
                Example 1:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Service not found
      x-stoplight:
        id: 1w97ger62fn8b
      description: 'If the amount associated with the package has already been paid, activation proceeds without generating a payment link. Otherwise, a payment link is generated for the user to complete the activation process.'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  package_id: c490edcf-cae2-4493-b9e4-f14b0eead60e
                  service_number: '************'
                  custom_amount: 500
                  redirect_url: 'https://us.app.myoperator.dev/cafs/paid/normal/2'
                  name: Shenoy
                  email: <EMAIL>
                  phone: '**********'
              required:
                - package_id
                - service_number
              properties:
                package_id:
                  type: string
                  format: uuid
                  description: This can be either a Global Package ID or a Custom Package ID.
                service_number:
                  type: string
                  example: '************'
                custom_amount:
                  type: number
                  description: Required when the user wants to make a partial payment.
                  format: float
                  minimum: 1
                  example: 1
                recharge_code:
                  type: string
                  x-stoplight:
                    id: ljvvkalevdbib
                  description: When user wants to use the payment code instead of payment link to activate the account.
                redirect_url:
                  type: string
                  format: uri
                  description: The user will be redirected to this URL after completing the payment process (regardless of success or failure).
                name:
                  type: string
                email:
                  type: string
                  format: email
                phone:
                  type: string
            examples:
              Example 1:
                value:
                  package_id: c490edcf-cae2-4493-b9e4-f14b0eead60e
                  service_number: '************'
                  custom_amount: 500
                  redirect_url: 'https://us.app.myoperator.dev/cafs/paid/normal/2'
                  name: Shenoy
                  email: <EMAIL>
                  phone: '**********'
      tags:
        - Service Activation
  '/services/{gsn}/billing_details':
    parameters:
      - schema:
          type: string
        name: gsn
        in: path
        required: true
        description: Company ID
    get:
      summary: Fetch Billing Info
      tags:
        - Services
      responses:
        '200':
          description: OK
          headers: {}
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    x-stoplight:
                      id: ocmv2u1vr15g5
                    example: success
                  code:
                    type: integer
                    x-stoplight:
                      id: ljan9nwh3drhw
                    example: 200
                  message:
                    type: string
                    x-stoplight:
                      id: vbl87dyxbszm5
                    example: Billing Account Info
                  data:
                    type: object
                    x-stoplight:
                      id: rd9aowe9ruhcf
                    properties:
                      billing_account_id:
                        type: string
                        x-stoplight:
                          id: rb91sbgc8r8s0
                        format: uuid
                      is_corporate:
                        type: boolean
                        x-stoplight:
                          id: fadpigo183tey
                      ac_number:
                        type: string
                        x-stoplight:
                          id: hi710njrdkre5
                        example: NTFY28
              examples:
                Non Corporate Account:
                  value:
                    status: success
                    code: 200
                    message: Billing Account Info
                    data:
                      billing_account_id: db2251e5-5308-40a7-9f48-3aed5a9ce736
                      is_corporate: false
                      ac_number: NTFY28
                Corporate Account:
                  value:
                    status: success
                    code: 200
                    message: Billing Account Info
                    data:
                      billing_account_id: db2251e5-5308-40a7-9f48-3aed5a9ce737
                      is_corporate: true
                      ac_number: NTFY29
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  code:
                    type: string
                  message:
                    type: string
                  errors:
                    type: object
                    properties:
                      detail:
                        type: string
                x-examples:
                  Example 1:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Not found.
              examples:
                Service Not Found:
                  value:
                    status: error
                    code: not_found
                    message: Not found.
                    errors:
                      detail: Not found.
      operationId: get-services-gsn-billing_details
      x-stoplight:
        id: p5q6uxat5f36d
      description: Fetch Billing info of a company
  '/core/countries/{id}/states':
    get:
      summary: List States
      tags:
        - Core
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                x-examples:
                  Example 1:
                    status: success
                    code: 200
                    message: States List
                    data: []
                properties:
                  status:
                    type: string
                  code:
                    type: integer
                  message:
                    type: string
                  pagination:
                    type: object
                    properties:
                      count:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      current:
                        type: integer
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/CountryState'
              examples:
                Success:
                  value:
                    status: success
                    code: 200
                    message: States List
                    pagination:
                      count: 1
                      per_page: 1
                      total_pages: 1
                      current: 1
                    data:
                      - id: 1
                        name: Himachal Pradesh
                        code: HP
                        code_for_gst: '02'
                        code_for_tin: 0
                        created: '2019-08-24T14:15:22Z'
                        modified: '2019-08-24T14:15:22Z'
        '404':
          $ref: '#/components/responses/Country_NotFound'
      operationId: get-core-countries
      x-stoplight:
        id: ln9b12cseqv5y
      requestBody:
        content:
          application/json:
            schema:
              type: object
            examples: {}
    parameters:
      - schema:
          type: string
        name: id
        in: path
        required: true
        description: Country ID
  /kyc/gst/send-otp:
    post:
      summary: GST Send OTP
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/GST-send-OTP-success'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
        '429':
          $ref: '#/components/responses/GST-send-OTP-limit-exceeded'
      operationId: post-kyc-gst-send-otp
      x-stoplight:
        id: 5yndskekutyhg
      description: |-
        Sends a 6-digit OTP on the phone number linked to GST number.

        NOTE: Maximum of 5 send OTP attempts are allowed within 24 hours.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  company_id: <company_id>
                  gst_number: <gst_number>
              required:
                - gst_number
              properties:
                gst_number:
                  type: string
                  description: The GST number.
            examples:
              Example 1:
                value:
                  gst_number: 27AAACR5055K2Z6
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  /kyc/gst/verify-otp:
    post:
      summary: GST Verify OTP
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/GST-verify-OTP-success'
        '400':
          $ref: '#/components/responses/GST-verify-OTP-bad-request'
        '404':
          $ref: '#/components/responses/GST-verify-OTP-not-found-or-expired'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
        '429':
          $ref: '#/components/responses/GST-verify-OTP-attempts-exceeded'
      operationId: post-kyc-gst-verify-otp
      x-stoplight:
        id: 7ggn6copah99k
      description: |-
        Verifies a 6-digit OTP sent on the phone number linked to GST number.

        NOTE: Maximum of 6 verify OTP attempts are allowed for a single OTP.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  gst_number: <gst_number>
                  otp: <otp>
              required:
                - gst_number
                - otp
              properties:
                gst_number:
                  type: string
                  description: The GST number.
                otp:
                  type: string
                  description: The 6-digit OTP.
            examples:
              Example 1:
                value:
                  gst_number: 27AAACR5055K2Z6
                  otp: '765123'
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  /kyc/aadhaar/send-otp:
    post:
      summary: Aadhaar Send OTP
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Aadhaar-send-OTP-success'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
        '429':
          $ref: '#/components/responses/Aadhaar-send-OTP-limit-exceeded'
      operationId: post-kyc-aadhaar-send-otp
      x-stoplight:
        id: gz4fi3ebgiy3o
      description: |-
        Sends a 6-digit OTP on the phone number linked to Aadhaar number.

        NOTE: Maximum send OTP attempts are with the vendor (Surepass).
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  aadhaar_number: '************'
              required:
                - aadhaar_number
              properties:
                aadhaar_number:
                  type: string
                  description: The Aadhaar number.
            examples:
              Example 1:
                value:
                  aadhaar_number: '************'
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  /kyc/aadhaar/verify-otp:
    post:
      summary: Aadhaar Verify OTP
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Aadhaar-verify-OTP-success'
        '400':
          $ref: '#/components/responses/Aadhaar-verify-OTP-bad-request'
        '404':
          $ref: '#/components/responses/Aadhaar-verify-OTP-not-found-or-expired'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
        '429':
          $ref: '#/components/responses/Aadhaar-verify-OTP-attempts-exceeded'
      operationId: post-kyc-aadhaar-verify-otp
      x-stoplight:
        id: b43q4i7vq85f4
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  aadhaar_number: '************'
                  otp: '765123'
              required:
                - aadhaar_number
                - otp
              properties:
                aadhaar_number:
                  type: string
                  description: The Aadhaar number.
                otp:
                  type: string
                  description: The 6-digit OTP.
            examples:
              Example 1:
                value:
                  aadhaar_number: '************'
                  otp: '765123'
      description: |-
        Verifies a 6-digit OTP sent on the phone number linked to Aadhaar number.

        NOTE: Maximum of 6 verify OTP attempts are allowed for a single valid OTP.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  '/kyc/{kyc_id}/gst/non-otp':
    post:
      summary: Save GST without OTP
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/GST-save-without-OTP-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: post-kyc-gst-non-otp
      x-stoplight:
        id: fd4yvv4xokkwa
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  gst_number: 27AAACR5055K2Z6
              required:
                - gst_number
              properties:
                gst_number:
                  type: string
                  description: The GST number.
            examples:
              Example 1:
                value:
                  gst_number: 27AAACR5055K2Z6
      description: Saves GST details and PDF without OTP.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
  '/kyc/{kyc_id}/task-status':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    get:
      summary: KYC task status
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/KYC-task-status'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: get-kyc-kyc_id-task-status
      x-stoplight:
        id: lzsny7rops6ui
      description: Fetches KYC task status.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  '/services/{company_id}/kyc':
    parameters:
      - schema:
          type: string
        name: company_id
        in: path
        required: true
        description: The company id.
    get:
      summary: KYC Details
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/KYC-details-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: get-services-gsn-kyc
      x-stoplight:
        id: c4pomipa8uisv
      description: Fetches most recent active KYC details.
      parameters:
        - schema:
            type: string
            enum:
              - kyc_states
          in: query
          description: '`kyc_states`. To fetch states of KYC state-machine.'
          name: expand
  '/kyc/{kyc_id}/pan/upload':
    post:
      summary: PAN upload
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/PAN-upload-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: 'post-kyc-:kyc_id-pan-upload'
      x-stoplight:
        id: 6bw423qwkie9s
      description: Uploads PAN document to backend storage (AWS S3).
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  x-stoplight:
                    id: wvg463msb8g4r
                  description: |-
                    The PAN document. 

                    Supported formats:

                    - PDF
                    - jpg
                    - png
                  type: object
            examples: {}
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
  '/kyc/{kyc_id}/digilocker/init':
    post:
      summary: Digilocker init
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Digilocker-init-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
      operationId: post-kyc-digilocker-init
      x-stoplight:
        id: et70w5eaufo5k
      description: Initializes Digilocker.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  mobile_number: '**********'
                  redirect_url: 'https://www.google.com'
              required:
                - mobile_number
                - redirect_url
              properties:
                mobile_number:
                  type: string
                  description: The mobile number.
                redirect_url:
                  type: string
                  description: The re-direct URL.
            examples:
              Example 1:
                value:
                  mobile_number: '**********'
                  redirect_url: 'https://www.google.com'
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
  '/kyc/{kyc_id}/digilocker/documents':
    post:
      summary: Digilocker documents
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Digilocker-documents-success'
        '400':
          $ref: '#/components/responses/Digilocker-documents-bad-request'
        '404':
          $ref: '#/components/responses/Digilocker-documents-PAN-not-found'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
      operationId: post-kyc-digilocker-documents
      x-stoplight:
        id: bmkcab0l2zl12
      description: |-
        - Fetches PAN PDF document from Digilocker API.

        - Triggers a background task to:

            - Invoke Digilocker Download document API

            - Download PAN PDF document
            
            - Upload PAN PDF to AWS S3
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  client_id: digilocker_dozAxmvVBdhGlurhcNNr
              required:
                - client_id
              properties:
                client_id:
                  type: string
                  description: The Digilocker client id.
            examples:
              Example 1:
                value:
                  client_id: digilocker_wTZJafwDbMfHoZMZalYX
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
  '/kyc/{kyc_id}':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    patch:
      summary: Update KYC
      tags:
        - KYC
      responses:
        '204':
          description: No Content
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: patch-kyc-id
      x-stoplight:
        id: 5t9vwymeo2xie
      description: Updates KYC details.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  status: 2
              properties:
                status:
                  enum:
                    - 2
                  description: |-
                    Updates the KYC status.

                    2: to mark KYC as failed
            examples:
              Mark KYC as failed:
                value:
                  status: 2
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
  '/kyc/{kyc_id}/human-verification/init':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    post:
      summary: Liveness init
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Liveness-init-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
      operationId: post-kyc-kyc_id-human-verification
      x-stoplight:
        id: h2ah9wi74y5gz
      description: Initializes Liveness for video KYC.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  eye_blink: false
                  assist_voice: false
                  head_movement_attempts: 4
                  frame_size: medium
              properties:
                eye_blink:
                  type: boolean
                  default: false
                  description: Eye blink check to be done or not. Defaults to false.
                assist_voice:
                  type: boolean
                  description: Voice assistance on or not. Defaults to false.
                  default: false
                head_movement_attempts:
                  type: integer
                  description: Number of attempts to be allowed for head movement. Defaults to 4.
                  default: 4
                frame_size:
                  description: The frame size of the video.
                  enum:
                    - small
                    - medium
                    - large
                  default: medium
            examples:
              Example 1:
                value:
                  eye_blink: false
                  assist_voice: false
                  head_movement_attempts: 4
                  frame_size: medium
  '/kyc/{kyc_id}/human-verification/confirm':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    post:
      summary: Liveness confirm
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/Liveness-confirm-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
        '424':
          $ref: '#/components/responses/External-API-dependency-failed'
      operationId: post-kyc-kyc_id-human-verification-confirm
      x-stoplight:
        id: w0lfds3s0wle9
      description: |-
        - Triggers a background task to:

            - Invoke Surepass liveness get report API

            - Download video from Surepass

            - Generates an image from the video
            
            - Upload video and image to AWS S3
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              x-examples:
                Example 1:
                  client_id: liveness_sdk_onadfasvadbnadtUbTANnMbBxZ
              required:
                - client_id
              properties:
                client_id:
                  type: string
                  description: The Liveness client id.
            examples:
              Example 1:
                value:
                  client_id: liveness_sdk_onadfasvadbnadtUbTANnMbBxZ
  '/kyc/{kyc_id}/e-sign/init':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    post:
      summary: E-sign init
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/E-sign-init-success'
        '400':
          $ref: '#/components/responses/E-sign-already-exists'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: post-kyc-kyc_id-e-sign-init
      x-stoplight:
        id: b3cvp5i21yi7g
      description: Initializes e-sign process.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
      requestBody:
        content: {}
  '/kyc/{kyc_id}/e-sign/submit':
    parameters:
      - schema:
          type: string
        name: kyc_id
        in: path
        required: true
        description: KYC ID
    post:
      summary: E-sign submit
      tags:
        - KYC
      responses:
        '200':
          $ref: '#/components/responses/E-sign-submit-success'
        '404':
          $ref: '#/components/responses/KYC-details-not-found'
      operationId: post-kyc-kyc_id-e-sign-submit
      x-stoplight:
        id: 8njiewriep25q
      description: Downloads all the documents from backend storage (AWS S3) and creates a new PDF with e-sign.
      parameters:
        - $ref: '#/components/parameters/BillingAccountIdParam'
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              x-examples:
                Example 1:
                  company_name: my_company_name
                  email: <EMAIL>
                  signature_img: {}
              properties:
                company_name:
                  type: string
                  description: The company name.
                email:
                  type: string
                  description: The email address.
                signature_img:
                  type: string
                  description: The signature image.
                  format: binary
            examples:
              Example 1:
                value:
                  company_name: my_company_name
                  email: <EMAIL>
                  signature_img: ...
components:
  schemas:
    Service:
      title: Service
      x-stoplight:
        id: jmm46qlusgbqg
      type: object
      required:
        - product_id
        - billing_account_id
        - gsn
        - user_profile_id
        - country_id
        - timezone
        - rent_per_month
        - renew_cycle
        - activation_date
        - expiry_date
        - live_status
        - status
        - current_usages
      properties:
        id:
          type: string
          x-stoplight:
            id: 8qmtdi19z592h
        product_id:
          type: string
          x-stoplight:
            id: pgmm1e9xhx7u4
        billing_account_id:
          type: string
          x-stoplight:
            id: 8dz2fuhmbkojb
        gsn:
          type: string
          x-stoplight:
            id: s4e698jvj8jzo
        user_profile_id:
          type: string
          x-stoplight:
            id: kr7wwpjtry0u1
          nullable: true
        country_id:
          type: integer
          x-stoplight:
            id: l14omo7nwyv46
        timezone:
          type: string
          x-stoplight:
            id: xk370oqhnhd51
        rent_per_month:
          type: number
          x-stoplight:
            id: ch5spshiniy69
          format: float
        renew_cycle:
          type: integer
          x-stoplight:
            id: ksdzdzn7dggko
          minimum: 1
        activation_date:
          type: string
          x-stoplight:
            id: 4su6g2vc451aj
          format: date-time
        expiry_date:
          type: string
          x-stoplight:
            id: 49euqmn3r2iv0
          format: date-time
        last_renewal:
          type: string
          x-stoplight:
            id: fuxxj7v0dlkof
          format: date-time
          nullable: true
        live_status:
          x-stoplight:
            id: 25y2r552uwr42
          enum:
            - demo
            - prepaid
            - postpaid
        status:
          x-stoplight:
            id: hn80e0gln0lvd
          enum:
            - active
            - inactive
            - suspended
        churn_date:
          type: string
          x-stoplight:
            id: dnrdg63y783bh
          format: date-time
          nullable: true
        current_usages:
          type: number
          x-stoplight:
            id: 00zk23rqvcpvg
          format: float
        created:
          type: string
          x-stoplight:
            id: ebebe6w39f7s9
          format: date-time
        modified:
          type: string
          x-stoplight:
            id: lu6m895hijqph
          format: date-time
        billing_account:
          x-stoplight:
            id: xxcy35udafg51
          oneOf:
            - $ref: '#/components/schemas/BillingAccount'
            - type: object
              x-stoplight:
                id: 3qomwmaw14quw
              nullable: true
    BillingAccount:
      title: BillingAccount
      x-stoplight:
        id: m5sq7g2l8avrw
      type: object
      required:
        - ac_number
        - state_id
        - business_name
        - org_type_id
        - billing_day
        - verification_state
        - status
      properties:
        id:
          type: string
          x-stoplight:
            id: ba4jlmr6cvpea
          format: uuid
          readOnly: true
        parent_id:
          type: string
          x-stoplight:
            id: vqkuqrtgmvp75
          format: uuid
          default: null
          nullable: true
          readOnly: true
        ac_number:
          type: string
          x-stoplight:
            id: v8jvdj0ihv1ab
          example: 4Z58SU
          readOnly: true
        state_id:
          type: integer
          x-stoplight:
            id: 5bwdflkiw62c5
        gst_no:
          type: string
          x-stoplight:
            id: zzptwamvxbcle
          maxLength: 15
          minLength: 0
          default: NA
          example: 18**********121
          nullable: true
          readOnly: true
        tan_no:
          type: string
          x-stoplight:
            id: 1ib7djgnumsco
          default: null
          nullable: true
        uan:
          type: string
          x-stoplight:
            id: a8p6k2w9stx04
          default: NA
          nullable: true
        business_name:
          type: string
          x-stoplight:
            id: d805s6p6nahg9
          example: VoiceTree Technologies Pvt Ltd
        org_type_id:
          type: integer
          x-stoplight:
            id: b05flwo61124t
          example: 10
        billing_day:
          type: integer
          x-stoplight:
            id: bq4o229g3691v
          example: 1
          readOnly: true
        credit_limit:
          type: integer
          x-stoplight:
            id: wopw41ghx6uth
          example: 100
        min_bal:
          type: number
          x-stoplight:
            id: e2k7yfp79nti4
          format: float
          default: 0
        recharge_on_min_bal:
          type: number
          x-stoplight:
            id: zw4qw45kgscwz
          format: float
          default: 0
        auto_bill_email:
          type: boolean
          default: true
        auto_bill_sms:
          type: boolean
          default: true
        cr_limit_email:
          type: boolean
          default: true
        cr_limit_sms:
          type: boolean
          default: true
        business_pan:
          type: string
          x-stoplight:
            id: st5jwjkff257n
          example: **********
        business_city:
          type: string
          x-stoplight:
            id: xc9jtymr2ylft
          example: Delhi
        business_state:
          type: string
          x-stoplight:
            id: t18vd3lzgj2lf
          example: New Delhi
        business_pincode:
          type: string
          x-stoplight:
            id: v7rdx635f8dde
          example: '110006'
        business_country:
          type: string
          x-stoplight:
            id: ikpljcxxi1q4f
          example: India
        business_address:
          type: string
          x-stoplight:
            id: 2ow952vba1ef5
          example: '345, 1st Floor'
        business_type:
          type: string
          x-stoplight:
            id: 8170iikkh228n
          default: NA
          example: NA
          nullable: true
        billing_property:
          type: integer
          x-stoplight:
            id: 1vxhnzgkumq0p
          default: 3
        account_manager:
          type: string
          x-stoplight:
            id: 0z095nuw5q1zd
          format: uuid
          nullable: true
        applied_period:
          type: integer
          x-stoplight:
            id: qypa0ffop5a8n
          default: 0
          readOnly: true
        total_pending:
          type: number
          x-stoplight:
            id: qre680i22avo7
          format: float
          readOnly: true
        verification_state:
          x-stoplight:
            id: g39o7o79z8lds
          enum:
            - verified
            - unverified
            - spam
            - fraud
          default: unverified
        status:
          x-stoplight:
            id: 3m3dqg9z3ztpf
          enum:
            - active
            - inactive
        created:
          type: string
          x-stoplight:
            id: do7vs53bsuqdb
          format: date-time
          readOnly: true
        modified:
          type: string
          x-stoplight:
            id: s4vsvxk5o728h
          format: date-time
          readOnly: true
    Package:
      title: Package
      x-stoplight:
        id: cezt3uy4rhdfp
      type: object
      description: Package Details
      required:
        - id
        - product_id
        - package_custom_id
        - name
        - package_category_id
        - code
        - package_type
        - rent_per_month
        - renew_cycle
        - is_public
        - ocs_flag
        - package_for
        - package_number
        - status
        - created
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        package_custom_id:
          type: string
          format: uuid
        name:
          type: string
        package_category_id:
          type: integer
        code:
          type: string
        description:
          type: string
          x-stoplight:
            id: 3rdzhfr7q7i5v
          nullable: true
        package_type:
          type: string
        rent_per_month:
          type: string
        renew_cycle:
          type: integer
        is_public:
          type: boolean
        ocs_flag:
          enum:
            - account
            - ocs
            - myoperator_account
            - none
        package_for:
          enum:
            - tollfree
            - virtual_number
            - mobile_tracking
            - heyo
        package_number:
          type: string
        discount:
          type: string
        status:
          enum:
            - active
            - inactive
        created:
          type: string
          format: date-time
    PackageFeature:
      title: PackageFeature
      x-stoplight:
        id: 4odbp0rk72nmj
      type: object
      x-examples: {}
      required:
        - product_feature_id
        - free_unit
        - additional
        - rent_per_month
        - status
        - created
      properties:
        id:
          type: string
          format: uuid
        product_feature_id:
          type: string
          x-stoplight:
            id: c3zm4hsgyk9fj
          format: uuid
        product_feature_property_id:
          type: string
          default: null
          nullable: true
        free_unit:
          type: integer
        additional:
          type: integer
        rent_per_month:
          type: string
          example: '199.000'
        status:
          enum:
            - enabled
            - disabled
        last_disabled_date:
          type: string
          default: null
          nullable: true
        resource_key:
          type: string
          default: null
          nullable: true
        memcache_key:
          type: string
          default: null
          nullable: true
        created:
          type: string
          format: date-time
        product_feature:
          $ref: '#/components/schemas/ProductFeature'
        rate_slabs:
          type: array
          x-stoplight:
            id: 99jcmuf0ckqon
          items:
            $ref: '#/components/schemas/PackageFeatureRateSlab'
      description: ''
    GlobalPackage:
      title: GlobalPackage
      x-stoplight:
        id: 5ggolunvop9mx
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        package_category_id:
          type: integer
        name:
          type: string
        package_type:
          type: string
        rent_per_month:
          type: string
        renew_cycle:
          type: integer
        is_public:
          type: boolean
        code:
          type: string
        status:
          enum:
            - active
            - inactive
        ocs_flag:
          enum:
            - account
            - ocs
            - myoperator_account
            - none
        package_for:
          enum:
            - tollfree
            - virtual_number
            - mobile_tracking
            - heyo
        package_number:
          type: string
        discount:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        created:
          type: string
          format: date-time
    GlobalPackageFeature:
      title: GlobalPackageFeature
      x-stoplight:
        id: 7ggsehuk4qn7u
      type: object
      required:
        - id
        - product_feature_id
        - product_feature_property_id
        - free_unit
        - rent_per_month
        - status
        - created
        - modified
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        product_feature_id:
          type: string
          format: uuid
          x-stoplight:
            id: el7hbx0kpjvpc
          readOnly: true
        product_feature_property_id:
          type: string
          x-stoplight:
            id: oyt6dv8rfsjh9
          format: uuid
          nullable: true
        free_unit:
          type: integer
          minimum: 0
        rent_per_month:
          type: string
          example: '199.000'
        status:
          enum:
            - enabled
            - disabled
        last_disabled_date:
          type: string
          format: date-time
          nullable: true
        resource_key:
          type: string
          default: null
          nullable: true
        memcache_key:
          type: string
          default: null
          nullable: true
        created:
          type: string
          format: date-time
          readOnly: true
        modified:
          type: string
          x-stoplight:
            id: k0w4gmie30rav
          format: date-time
          readOnly: true
        product_feature:
          $ref: '#/components/schemas/ProductFeature'
        rate_slabs:
          type: array
          x-stoplight:
            id: jzkjiovozcqj4
          items:
            $ref: '#/components/schemas/PackageFeatureRateSlab'
    PackageFeatureRateSlab:
      title: PackageFeatureRate
      x-stoplight:
        id: dfhpgpnulfer1
      type: object
      properties:
        id:
          type: string
          format: uuid
        package_feature_id:
          type: string
          format: uuid
        min:
          type: integer
          minimum: 0
        max:
          type: integer
          minimum: 0
        rate:
          type: string
          example: '0.000'
        status:
          enum:
            - active
            - inactive
        created:
          type: string
          format: date-time
    ProductFeature:
      title: ProductFeature
      x-stoplight:
        id: vuwqoisfx8oa8
      type: object
      x-examples: {}
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        name:
          type: string
        unit:
          type: string
        billing_type:
          enum:
            - 'N'
            - L
            - E
            - F
        resource_key:
          type: string
          default: null
          nullable: true
        memcache_key:
          type: string
          default: null
          nullable: true
        type:
          type: integer
          default: 1
        is_highlighted:
          type: integer
          default: 0
        status:
          enum:
            - active
            - inactive
        created:
          type: string
          format: date-time
      description: ''
    ProductFeatureProperty:
      title: ProductFeatureProperty
      x-stoplight:
        id: n9wuzzydl2fhd
      type: object
      properties:
        id:
          type: string
          x-stoplight:
            id: nes298f6mmv5k
    PackageCategory:
      title: PackageCategory
      x-stoplight:
        id: qalcpkuqd5vr1
      type: object
      properties:
        id:
          type: integer
          x-stoplight:
            id: ekcup3bk6mwz4
          minimum: 1
        product_id:
          type: string
          x-stoplight:
            id: dr20fvfubqdsq
          format: uuid
        code:
          x-stoplight:
            id: 1wcvv14yx7a4n
          enum:
            - L
            - D
        name:
          type: string
          x-stoplight:
            id: sk13so0zlnkxs
        description:
          type: string
          x-stoplight:
            id: mm0embcjg1jhl
          default: null
          nullable: true
        weightage:
          type: integer
          x-stoplight:
            id: fwqdeklnwdio6
          default: 1
        created:
          type: string
          x-stoplight:
            id: 0sg4cy6ak3fv1
          format: date-time
        modified:
          type: string
          x-stoplight:
            id: 3n70bmqerw2s3
          format: date-time
      description: ''
    CountryState:
      title: CountryState
      x-stoplight:
        id: jwn0whilks15h
      type: object
      required:
        - id
        - name
        - code
        - code_for_gst
      properties:
        id:
          type: integer
          x-stoplight:
            id: 4cyhbznnf3jz3
          minimum: 1
          example: 1
          readOnly: true
        name:
          type: string
          x-stoplight:
            id: e7zcuguanhbvb
          minLength: 1
          maxLength: 200
          example: Himachal Pradesh
        code:
          type: string
          x-stoplight:
            id: f24wihzlscd2v
          minLength: 1
          maxLength: 2
          example: HP
        code_for_gst:
          type: string
          x-stoplight:
            id: 1tv65af2qkcg4
          example: '02'
          minLength: 1
          maxLength: 2
        code_for_tin:
          type: integer
          x-stoplight:
            id: w3rxfl14b2inv
        created:
          type: string
          x-stoplight:
            id: yiwsd7qfc7g2i
          format: date-time
          readOnly: true
        modified:
          type: string
          x-stoplight:
            id: qlqbvf9bl4dlb
          format: date-time
          readOnly: true
    TaxDetails:
      title: TaxDetails
      x-stoplight:
        id: 2plcm9ndey74n
      type: object
      properties:
        name:
          x-stoplight:
            id: s05nm0b9t7hi8
          example: GST
          enum:
            - GST
            - IGST
            - CGST
            - SGST
            - VAT
        tax_percent:
          type: number
          x-stoplight:
            id: jiyiezv59kj2o
          format: float
          example: 18
          minimum: 0
        amount:
          type: number
          x-stoplight:
            id: c4seuxgoi4xpy
          format: float
          example: 33.82
          minimum: 0
    KYC:
      type: object
      x-examples:
        Example 1:
          id: 3fc3979351d74992bd8e1ddb8bc6c6e4
          billing_account_id: 682d5c9f0c0dd430
          current_state: completed
          mode: gst
          source: myoperator
          status: 1
          expiry: '2026-05-22T14:15:22Z'
          created: '2025-05-22T14:15:22Z'
          modified: '2025-05-22T14:15:22Z'
          kyc_states:
            verification:
              gst: video_kyc
              aadhaar:
                gst_info: video_kyc
                upload_pan: video_kyc
                digilocker_pan: video_kyc
            video_kyc: e_sign
            e_sign: kyc_done
            kyc_done: null
      examples:
        - id: 3fc3979351d74992bd8e1ddb8bc6c6e4
          billing_account_id: 682d5c9f0c0dd430
          current_state: kyc_done
          mode: gst
          source: myoperator
          status: completed
          expiry: '2026-05-22T14:15:22Z'
          created: '2025-05-22T14:15:22Z'
          modified: '2025-05-22T14:15:22Z'
          kyc_states:
            verification:
              gst: video_kyc
              aadhaar:
                gst_info: video_kyc
                upload_pan: video_kyc
                digilocker_pan: video_kyc
            video_kyc: e_sign
            e_sign: kyc_done
            kyc_done: null
      required:
        - id
        - billing_account_id
        - current_state
        - mode
        - source
        - status
        - expiry
        - created
        - modified
      properties:
        id:
          type: string
          description: KYC ID
        billing_account_id:
          type: string
          description: The Billing account id.
        current_state:
          description: The KYC current state.
          enum:
            - verification
            - gst_info
            - upload_pan
            - digilocker_pan
            - video_kyc
            - e_sign
            - kyc_done
        mode:
          description: The KYC mode.
          enum:
            - gst
            - aadhaar
        source:
          description: The KYC source.
          enum:
            - myoperator
            - heyo
            - accounts
        status:
          description: The KYC status.
          enum:
            - pending
            - completed
            - failed
            - expired
        expiry:
          type:
            - string
            - 'null'
          description: The KYC expiry date-time.
        created:
          type: string
          description: The KYC created date-time.
        modified:
          type: string
          description: The KYC modified date-time.
        kyc_states:
          type: object
          description: The KYC states. Returned only when requested.
          required:
            - verification
            - video_kyc
            - e_sign
            - kyc_done
          properties:
            verification:
              type: object
              required:
                - gst
                - aadhaar
              properties:
                gst:
                  type: string
                aadhaar:
                  type: object
                  required:
                    - gst_info
                    - upload_pan
                    - digilocker_pan
                  properties:
                    gst_info:
                      type: string
                    upload_pan:
                      type: string
                    digilocker_pan:
                      type: string
            video_kyc:
              type: string
            e_sign:
              type: string
            kyc_done:
              type: 'null'
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                properties:
                  name:
                    type: array
                    items:
                      type: object
                      properties: {}
            x-examples:
              Example 1:
                status: error
                code: ACC_0002
                message: Validation Failed
                errors:
                  name: []
          examples: {}
        application/xml:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                properties:
                  name:
                    type: array
                    items:
                      type: object
                      properties: {}
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              errors:
                type: object
                properties: {}
            x-examples:
              Example 1:
                status: error
                code: 403
                message: Invalid token
                errors: {}
          examples: {}
        application/xml:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              errors:
                type: object
                properties: {}
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                properties:
                  detail:
                    type: string
            x-examples:
              Example 1:
                status: error
                code: ACC_0001
                message: Interal Server Error
                errors:
                  detail: ''
          examples: {}
        application/xml:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                properties:
                  detail:
                    type: string
    BillingAccount_NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
                x-stoplight:
                  id: ak8qujbyhzpjn
              code:
                type: string
                x-stoplight:
                  id: byzjhet27fo15
              message:
                type: string
                x-stoplight:
                  id: g3mk5xf1iql2r
              errors:
                type: object
                x-stoplight:
                  id: vj2j836b8efeb
                properties:
                  detail:
                    type: string
                    x-stoplight:
                      id: xtcux34v500mo
          examples:
            Invalid Billing Account:
              value:
                status: error
                code: ACC_404
                message: Invalid Billing Account
                error:
                  detail: Invalid Billing Account
    Country_NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              error:
                type: object
                properties:
                  detail:
                    type: string
            x-examples:
              Example 1:
                status: error
                code: ACC_404
                message: Invalid Country ID
                error:
                  detail: Invalid Country ID
          examples:
            Invalid Country ID:
              value:
                status: error
                code: ACC_404
                message: Invalid Country ID
                error:
                  detail: Invalid Country ID
    GST-send-OTP-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: OTP sent successfully on the phone number linked to GST number.
                data:
                  masked_phone_number: '*******543'
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - masked_phone_number
                properties:
                  masked_phone_number:
                    type: string
                    description: The masked phone number linked to GST number.
          examples:
            OTP sent successfully:
              value:
                status: success
                code: 200
                message: OTP sent successfully on the phone number linked to GST number.
                data:
                  masked_phone_number: '*******543'
      headers:
        attempts_left:
          schema:
            type: string
          description: Send OTP attempts left.
    GST-verify-OTP-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: OTP verified successfully.
                data:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
                    x-stoplight:
                      id: muxlex43q9z5v
                    description: The KYC task id.
          examples:
            OTP verified successfully:
              value:
                status: success
                code: 200
                message: OTP verified successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
      headers:
        attempts_left:
          schema:
            type: string
          description: Verify OTP attempts left.
    GST-verify-OTP-not-found-or-expired:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0501
                message: OTP data not found or expired.
                errors:
                  detail: OTP data not found or expired.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            OTP data not found or expired:
              value:
                status: error
                code: ACC_0503
                message: OTP data not found or expired.
                errors:
                  detail: OTP data not found or expired.
    GST-verify-OTP-bad-request:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0502
                message: Input OTP does not match.
                errors:
                  detail: Input OTP does not match.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            OTP does not match:
              value:
                status: error
                code: ACC_0502
                message: Input OTP does not match.
                errors:
                  detail: Input OTP does not match.
    GST-send-OTP-limit-exceeded:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0503
                message: 'Send OTP limit exceeded. Maximum allowed: 5'
                errors:
                  detail: 'Send OTP limit exceeded. Maximum allowed: 5'
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            Send OTP limit exceeded:
              value:
                status: error
                code: ACC_0501
                message: 'Send OTP limit exceeded. Maximum allowed: 5'
                errors:
                  detail: 'Send OTP limit exceeded. Maximum allowed: 5'
    GST-verify-OTP-attempts-exceeded:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0503
                message: 'OTP verification attempts exceeded. Maximum allowed: 6'
                errors:
                  detail: 'OTP verification attempts exceeded. Maximum allowed: 6'
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            OTP verification attempts exceeded:
              value:
                status: error
                code: ACC_0504
                message: 'OTP verification attempts exceeded. Maximum allowed: 6'
                errors:
                  detail: 'OTP verification attempts exceeded. Maximum allowed: 6'
    External-API-dependency-failed:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0424
                message: External API dependency failed.
                errors:
                  detail: External API dependency failed.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            External API dependency failed:
              value:
                status: error
                code: ACC_0424
                message: External API dependency failed.
                errors:
                  detail: External API dependency failed.
    Aadhaar-send-OTP-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: OTP sent successfully on the phone number linked to Aadhaar number.
                data: null
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type:
                  - object
                  - 'null'
          examples:
            OTP sent successfully:
              value:
                status: success
                code: 200
                message: OTP sent successfully on the phone number linked to Aadhaar number.
                data: null
      headers: {}
    Aadhaar-send-OTP-limit-exceeded:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0501
                message: Send OTP limit exceeded.
                errors:
                  detail: Send OTP limit exceeded.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            Send OTP limit exceeded:
              value:
                status: error
                code: ACC_0501
                message: Send OTP limit exceeded.
                errors:
                  detail: Send OTP limit exceeded.
    Aadhaar-verify-OTP-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: OTP verified successfully.
                data:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
                    x-stoplight:
                      id: ssjf590yat3u8
                    description: The KYC task id.
          examples:
            OTP verified successfully:
              value:
                status: success
                code: 200
                message: OTP verified successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
      headers:
        attempts_left:
          schema:
            type: string
          description: Verify OTP attempts left.
    Aadhaar-verify-OTP-attempts-exceeded:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0504
                message: 'OTP verification attempts exceeded. Maximum allowed: 6'
                errors:
                  detail: 'OTP verification attempts exceeded. Maximum allowed: 6'
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            OTP verification attempts exceeded:
              value:
                status: error
                code: ACC_0504
                message: 'OTP verification attempts exceeded. Maximum allowed: 6'
                errors:
                  detail: 'OTP verification attempts exceeded. Maximum allowed: 6'
    GST-save-without-OTP-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: GST number without OTP saved successfully.
                data:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
                    description: The KYC task id.
          examples:
            GST number without OTP saved successfully:
              value:
                status: success
                code: 200
                message: GST number without OTP saved successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
    KYC-details-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: KYC details fetched successfully.
                data:
                  id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  billing_account_id: 682d5c9f0c0dd430
                  current_state: completed
                  mode: gst
                  source: myoperator
                  expiry: '2026-05-22T14:15:22Z'
                  status: 1
                  created: '2025-05-22T14:15:22Z'
                  modified: '2025-05-22T14:15:22Z'
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                $ref: '#/components/schemas/KYC'
          examples:
            KYC details fetched successfully:
              value:
                status: success
                code: 200
                message: KYC details fetched successfully.
                data:
                  id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  billing_account_id: 682d5c9f0c0dd430
                  current_state: kyc_done
                  mode: gst
                  source: myoperator
                  status: completed
                  expiry: '2026-05-22T14:15:22Z'
                  created: '2025-05-22T14:15:22Z'
                  modified: '2025-05-22T14:15:22Z'
                  kyc_states:
                    verification:
                      gst: video_kyc
                      aadhaar:
                        gst_info: video_kyc
                        upload_pan: video_kyc
                        digilocker_pan: video_kyc
                    video_kyc: e_sign
                    e_sign: kyc_done
                    kyc_done: null
    Digilocker-init-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: Digilocker initialized successfully.
                data:
                  client_id: digilocker_dozAxmvVBdhGlurhcNNr
                  url: 'https://digilocker-sdk.notbot.in/?gateway=sandbox&type=digilocker&token=.eJyrVkrOyUzNK4nPTFGyUkrJTM_MyU_OTi2KT8mvcqzILQtzSslwzyktykj28ytS0lFKTyxJLU-sBKotTsxLScqvAIqVVBakomhWqgUAckAfPg.aCSJCg.bBZnfrVNZI5WYbe0T4Zeup_o_0Y&auth_type=web'
                  expiry_seconds: 1200
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - client_id
                  - url
                  - expiry_seconds
                properties:
                  client_id:
                    type: string
                    description: The Digilocker client id.
                  url:
                    type: string
                    description: The Digilocker URL.
                  expiry_seconds:
                    type: integer
                    description: The Digilocker URL expiry.
          examples:
            Digilocker initialized successfully:
              value:
                status: success
                code: 200
                message: Digilocker initialized successfully.
                data:
                  client_id: digilocker_dozAxmvVBdhGlurhcNNr
                  url: 'https://digilocker-sdk.notbot.in/?gateway=sandbox&type=digilocker&token=.eJyrVkrOyUzNK4nPTFGyUkrJTM_MyU_OTi2KT8mvcqzILQtzSslwzyktykj28ytS0lFKTyxJLU-sBKotTsxLScqvAIqVVBakomhWqgUAckAfPg.aCSJCg.bBZnfrVNZI5WYbe0T4Zeup_o_0Y&auth_type=web'
                  expiry_seconds: 1200
    Digilocker-documents-bad-request:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0505
                message: Invalid Digilocker client_id provided.
                errors:
                  detail: Invalid Digilocker client_id provided.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            Invalid Digilocker client_id provided:
              value:
                status: error
                code: ACC_0505
                message: Invalid Digilocker client_id provided.
                errors:
                  detail: Invalid Digilocker client_id provided.
    Digilocker-documents-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: Digilocker PAN document processing started.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
                    description: The KYC task id.
          examples:
            Digilocker PAN document processing started successfully:
              value:
                status: success
                code: 200
                message: Digilocker PAN document processing started successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
    Digilocker-documents-PAN-not-found:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0506
                message: Digilocker PAN document not found.
                errors:
                  detail: Digilocker PAN document not found.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            Digilocker PAN document not found:
              value:
                status: error
                code: ACC_0506
                message: Digilocker PAN document not found.
                errors:
                  detail: Digilocker PAN document not found.
    KYC-task-status:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                kyc_state: verification
                status: failed
                data:
                  is_verified: false
                  failure_reason: ''
            required:
              - kyc_state
              - status
              - data
            properties:
              kyc_state:
                description: The KYC state.
                enum:
                  - verification
                  - gst_info
                  - upload_pan
                  - digilocker_pan
                  - video_kyc
                  - e_sign
                  - completed
              status:
                description: The KYC status.
                enum:
                  - pending
                  - completed
                  - failed
              data:
                type:
                  - object
                  - 'null'
                properties:
                  is_verified:
                    type: boolean
                  failure_reason:
                    type: string
                  caf_number:
                    type: string
                    x-stoplight:
                      id: 7o03jiwsj2tcq
                    description: The E-sign CAF number in case of e_sign KYC state.
                  address:
                    type: string
                    x-stoplight:
                      id: 359j749fuhz1j
                  area:
                    type: string
                    x-stoplight:
                      id: fwtaqkb7l7jas
                  city:
                    type: string
                    x-stoplight:
                      id: 4sd6b4a5c03k9
                  state:
                    type: string
                    x-stoplight:
                      id: 34tyz0rso9nma
                  pin:
                    type: string
                    x-stoplight:
                      id: u07rjxu4m515m
                  gst:
                    type: string
                    x-stoplight:
                      id: u34kgtcsi9tf1
                  video_kyc_image:
                    type: string
                    x-stoplight:
                      id: gs6yw4j2n0gyo
                    description: The video KYC image in case of e_sign KYC state.
                    format: uri
          examples:
            verification pending:
              value:
                kyc_state: verification
                status: pending
                data: null
            verification completed:
              value:
                kyc_state: verification
                status: completed
                data:
                  is_verified: true
            verification failed:
              value:
                kyc_state: verification
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
            gst_info pending:
              value:
                kyc_state: gst_info
                status: pending
                data: null
            gst_info completed:
              value:
                kyc_state: gst_info
                status: completed
                data:
                  is_verified: true
            gst_info failed:
              value:
                kyc_state: gst_info
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
            upload_pan pending:
              value:
                kyc_state: upload_pan
                status: pending
                data: null
            upload_pan completed:
              value:
                kyc_state: upload_pan
                status: completed
                data:
                  is_verified: true
            upload_pan failed:
              value:
                kyc_state: upload_pan
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
            digilocker_pan pending:
              value:
                kyc_state: digilocker_pan
                status: pending
                data: null
            digilocker_pan completed:
              value:
                kyc_state: digilocker_pan
                status: completed
                data:
                  is_verified: true
            digilocker_pan failed:
              value:
                kyc_state: digilocker_pan
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
            video_kyc pending:
              value:
                kyc_state: video_kyc
                status: pending
                data: null
            video_kyc completed:
              value:
                kyc_state: video_kyc
                status: completed
                data:
                  is_verified: true
            video_kyc failed:
              value:
                kyc_state: video_kyc
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
            e_sign pending:
              value:
                kyc_state: e_sign
                status: pending
                data:
                  caf_number: my_caf_number
                  address: my_address
                  area: my_area
                  city: my_city
                  state: my_state
                  pin: my_pin
                  gst: 27AAACR5055K2Z6
                  video_kyc_image: 'https://s3.aws.com/user.jpg'
            e_sign completed:
              value:
                kyc_state: e_sign
                status: completed
                data:
                  is_verified: true
                  caf_number: my_caf_number
                  address: my_address
                  area: my_area
                  city: my_city
                  state: my_state
                  pin: my_pin
                  gst: 27AAACR5055K2Z6
                  video_kyc_image: 'https://s3.aws.com/user.jpg'
            e_sign failed:
              value:
                kyc_state: e_sign
                status: failed
                data:
                  is_verified: false
                  failure_reason: ...
    PAN-upload-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: PAN upload process started successfully.
                data:
                  kyc_id: 3fc3979351d74992bd8e1ddb8bc6c6e4
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
          examples:
            PAN upload process started successfully:
              value:
                status: success
                code: 200
                message: PAN upload process started successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
    Aadhaar-verify-OTP-not-found-or-expired:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0503
                message: OTP data not found or expired.
                errors:
                  detail: OTP data not found or expired.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            OTP data not found or expired:
              value:
                status: error
                code: ACC_0503
                message: OTP data not found or expired.
                errors:
                  detail: OTP data not found or expired.
    KYC-details-not-found:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0507
                message: KYC details not found.
                errors:
                  detail: KYC details not found.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            KYC details not found:
              value:
                status: error
                code: ACC_0507
                message: KYC details not found.
                errors:
                  detail: KYC details not found.
    Liveness-init-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: Liveness initialized successfully.
                data:
                  client_id: liveness_sdk_onadfasvadbnadtUbTANnMbBxZ
                  token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTadfasdfadsgsadcadsgasvsagahrnyfjsfbdfdbavarvaS_M
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - client_id
                  - token
                properties:
                  client_id:
                    type: string
                    description: The Liveness client id.
                  token:
                    type: string
                    description: The Liveness token.
          examples:
            Liveness initialized successfully:
              value:
                status: success
                code: 200
                message: Liveness initialized successfully.
                data:
                  client_id: liveness_sdk_onadfasvadbnadtUbTANnMbBxZ
                  token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTadfasdfadsgsadcadsgasvsagahrnyfjsfbdfdbavarvaS_M
    Liveness-confirm-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: Liveness process started successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
                    description: The Liveness task id.
          examples:
            Liveness process started successfully:
              value:
                status: success
                code: 200
                message: Liveness process started successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
    E-sign-already-exists:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0508
                message: E-sign already exists.
                errors:
                  detail: E-sign already exists.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            E-sign already exists:
              value:
                status: error
                code: ACC_0508
                message: E-sign already exists.
                errors:
                  detail: E-sign already exists.
    E-sign-init-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: E-sign initialized successfully.
                data:
                  caf_number: my_caf_number
                  address: my_address
                  area: my_area
                  city: my_city
                  state: my_state
                  pin: my_pin
                  gst: 27AAACR5055K2Z6
                  video_kyc_image: ...
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - caf_number
                  - address
                  - area
                  - city
                  - state
                  - pin
                  - gst
                  - video_kyc_image
                properties:
                  caf_number:
                    type: string
                    description: The E-sign CAF number.
                  address:
                    type: string
                  area:
                    type: string
                  city:
                    type: string
                  state:
                    type: string
                  pin:
                    type: string
                  gst:
                    type: string
                    description: The GST number.
                  video_kyc_image:
                    type: string
                    description: The video KYC image.
                    format: uri
          examples:
            E-sign initialized successfully:
              value:
                status: success
                code: 200
                message: E-sign initialized successfully.
                data:
                  caf_number: my_caf_number
                  address: my_address
                  area: my_area
                  city: my_city
                  state: my_state
                  pin: my_pin
                  gst: 27AAACR5055K2Z6
                  video_kyc_image: 'https://s3.aws.com/user.jpg'
    E-sign-submit-success:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: success
                code: 200
                message: E-sign submitted successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
            required:
              - status
              - code
              - message
              - data
            properties:
              status:
                type: string
              code:
                type: integer
              message:
                type: string
              data:
                type: object
                required:
                  - task_id
                properties:
                  task_id:
                    type: string
          examples:
            E-sign process submitted successfully:
              value:
                status: success
                code: 200
                message: E-sign process submitted successfully.
                data:
                  task_id: ed2e310b-30fd-499a-9ebe-7e7dc1afc549
    Aadhaar-verify-OTP-bad-request:
      description: ''
      content:
        application/json:
          schema:
            type: object
            x-examples:
              Example 1:
                status: error
                code: ACC_0502
                message: Input OTP does not match.
                errors:
                  detail: Input OTP does not match.
            required:
              - status
              - code
              - message
              - errors
            properties:
              status:
                type: string
              code:
                type: string
              message:
                type: string
              errors:
                type: object
                required:
                  - detail
                properties:
                  detail:
                    type: string
          examples:
            Input OTP does not match:
              value:
                status: error
                code: ACC_0502
                message: Input OTP does not match.
                errors:
                  detail: Input OTP does not match.
  requestBodies: {}
  securitySchemes:
    Token:
      type: http
      scheme: bearer
  parameters:
    Authorization:
      name: Authorization
      in: header
      required: true
      schema:
        type: string
      description: Authorization Token
    PaginationLimit:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        default: 10
        maximum: 20
      description: Pagination Limit
    PaginationPage:
      name: page
      in: query
      required: false
      schema:
        type: integer
      description: Page Number
    FilterProductShortCode:
      name: product_short_code
      in: query
      required: false
      schema:
        type: string
        enum:
          - myopin
          - myopus
          - myopau
          - myopgb
          - myopsg
          - myopca
          - myopnz
          - myopit
          - myopae
          - myopph
          - myopde
          - myopat
          - myopmy
          - myopza
          - myopkr
          - myopjp
          - myopnl
          - myophk
          - heyoin
        example: myopin
      description: 'Filter by Product Short Code, Comma Separated values are allowed'
    FilterPackageCategoryID:
      name: package_category_id
      in: query
      required: false
      schema:
        type: string
        format: uuid
      description: Filter by Package Category ID
    FIlterPackageCatergoryCode:
      name: package_category_code
      in: query
      required: false
      schema:
        type: string
        example: L
        enum:
          - L
          - D
      description: Filter by Package Category Code
    BillingAccountIdParam:
      name: X-MYOP-BILLING-ACCOUNT-ID
      in: header
      required: true
      schema:
        type: string
      description: Billing Account ID
security:
  - Token: []