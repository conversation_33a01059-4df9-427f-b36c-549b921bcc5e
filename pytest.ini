[pytest]
addopts = --ds=config.settings.test --reuse-db --nomigrations --cov --cov-report=xml --cov-report=html --junitxml=reports/junit.xml
python_files = tests.py test_*.py

env =
    DATABASE_ENGINE=django.db.backends.sqlite3
    DATABASE_NAME=test_db
    DATABASE_USER=myop
    DATABASE_PASSWORD=password
    DATABASE_HOST=localhost
    DATABASE_PORT=3306
    CACHE_BACKEND=django.core.cache.backends.locmem.LocMemCache
    REDIS_URL=test_cache
    CELERY_BROKER_URL=test_cache

    NUMBER_SYSTEM_API_URL=http://localhost/
    NUMBER_SYSTEM_API_TOKEN=abc123
    
    MEMCACHE_API_URL=http://localhost/
    MEMCACHE_API_URL_US=http://localhost/

    ACCOUNT_API_V1_HOST=http://localhost/
    ACCOUNT_API_V1_SALT=abc123

    INTERNAL_API_USER_ID=123

    API_TIMEOUT=5

    DJANGO_AWS_ACCESS_KEY_ID=access_key
    DJANGO_AWS_SECRET_ACCESS_KEY=secret_key
    DJANGO_AWS_DEFAULT_REGION=region
    ACCOUNTS_SNS_ARN=arn

    MYOPERATOR_API_URL=http://localhost/
    MYOPERATOR_API_URL_US=http://localhost/
    MYOPERATOR_API_SALT=abc123

    MR_CRON=abc
    ST_CRON=xyz
    AS_CRON=123

    SNS_ARN_FEATURE_UPDATE=abc
    SNS_ARN_ACCOUNT_FEATURE=xyz

    DD_CRON=abc
    REACTIVATE_NOTE=abc
    DEACTIVATE_NOTE=xyz

    ACCOUNTS_PAYMENT_API=http://localhost/
    CHAT_API_URL=http://localhost/chat/

    RECHARGE_CONTACT_NAME=abc
    RECHARGE_CONTACT_EMAIL=xyz
    RECHARGE_CONTACT_NUMBER=123

    ACCOUNT_PAYMENT_GENERATION_API_URL=http://localhost/

    SUSPEND_NOTE=abc
    CL_CRON=abc
    CREDIT_LIMIT_EMAIL_ID=abc
    CREDIT_LIMIT_EMAIL_NAME=abc

    LOW_BALANCE_TEMPLATE=abc
    SERVICE_SUSPEND_TEMPLATE=abc
    CREDIT_LIMIT_TEMPLATE=abc
    SERVICE_SUSPEND_FAILURE_TEMPLATE=abc

    HEYO_PRODUCT_ID=123
    SMSG_API_HOST=http://localhost/
    TREUCALLER_API_HOST=http://localhost/

    BUBBLE_CONTACT_SYNC_API_HOST=http://localhost/
    BILLING_EVENT_SQS_QUEUE_URL=http://aws.sqs.local
    BILLING_EVENT_SQS_MAX_ITER=1
    SUREPASS_API_HOST=http://surepass.local/
    SUREPASS_API_TOKEN=abc123