{"info": {"_postman_id": "7543abbd-c567-43a5-aa62-268cff84b4ad", "name": "kyc", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "26747662"}, "item": [{"name": "vendor", "item": [{"name": "PAN fetch details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id_number\": \"zzz\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/pan/pan-comprehensive", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/pan/pan-comprehensive' \\\n--header 'Authorization: Bearer TOKEN' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"id_number\": \"**********\"\n}'\n"}, "response": [{"name": "PAN Fetch details", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id_number\": \"**********\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/pan/pan-comprehensive"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 14 May 2025 11:16:55 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "629"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self' *.surepass.io; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.surepass.io blob:;"}, {"key": "Permissions-Policy", "value": "accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(), usb=(), web-share=(), xr-spatial-tracking=(), clipboard-read=(), clipboard-write=(), gamepad=(), speaker-selection=(), conversion-measurement=(), focus-without-user-activation=(), hid=(), idle-detection=(), interest-cohort=(), serial=(), sync-script=(), trust-token-redemption=(), window-placement=(), vertical-scroll=()"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-Client-ID", "value": "pan_comprehensive_HadddQvmfPnxwsbYxdxT"}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"pan_comprehensive_HadddQvmfPnxwsbYxdxT\",\n        \"pan_number\": \"**********\",\n        \"full_name\": \"ANUBHAV BAWEJA\",\n        \"full_name_split\": [\n            \"ANUBHAV\",\n            \"\",\n            \"BAWEJA\"\n        ],\n        \"masked_aadhaar\": \"XXXXXXXX3987\",\n        \"address\": {\n            \"line_1\": \"\",\n            \"line_2\": \"\",\n            \"street_name\": \"\",\n            \"zip\": \"\",\n            \"city\": \"\",\n            \"state\": \"\",\n            \"country\": \"\",\n            \"full\": \"\"\n        },\n        \"email\": null,\n        \"phone_number\": null,\n        \"gender\": \"M\",\n        \"dob\": \"1996-09-21\",\n        \"input_dob\": null,\n        \"aadhaar_linked\": true,\n        \"dob_verified\": false,\n        \"dob_check\": false,\n        \"category\": \"person\",\n        \"status\": \"valid\",\n        \"less_info\": false\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": null,\n    \"message_code\": \"success\"\n}"}]}, {"name": "PAN fetch PDF report", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n   \"client_id\": \"pan_comprehensive_dwhbaPOnvyrotiRpglif\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/pan/pan-pdf-report", "description": "Generated from cURL: curl --location 'https://sandbox.surepass.io/api/v1/pan/pan-pdf-report' \\\n--header 'Content-Type: application/json' \\\n--header 'Accept: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n   \"client_id\": \"PAN_CLIENT_ID\"\n}'"}, "response": []}, {"name": "PAN fetch details from file (OCR)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Downloads/pan_pdf_report_1747132542765976.pdf"}]}, "url": "https://sandbox.surepass.io/api/v1/ocr/pan", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/ocr/pan' \\\n--form 'file=@\"/path/to/file\"'\n"}, "response": []}, {"name": "D GST fetch details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"id_number\": \"27**********2Z6\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/corporate/gstin-advanced", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/corporate/gstin-advanced' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n    \"id_number\": \"27**********2Z6\"\n}'\n"}, "response": [{"name": "GST Fetch details", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"id_number\": \"27**********2Z6\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/corporate/gstin-advanced"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Mon, 19 May 2025 10:36:40 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "1974"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": "corporate_gstin_advanced_komqMYgetsZMioGztvkQ"}], "cookie": [], "body": "{\n    \"data\": {\n        \"contact_details\": {\n            \"principal\": {\n                \"address\": \"Building 10A, 5 TTC Industrial Area, Reliance Corporate Park, Thane Belapur Road, Ghansoli, Navi Mumbai, Thane, Maharashtra, 400701\",\n                \"email\": \"<EMAIL>\",\n                \"mobile\": \"9987002531\",\n                \"nature_of_business\": \"Recipient of Goods or Services, Supplier of Services, Export, Office / Sale Office, Import\"\n            },\n            \"additional\": []\n        },\n        \"promoters\": [\n            \"Madhusudana Sivaprasad Panda \",\n            \"Mukesh D<PERSON>ubhai <PERSON>i \",\n            \"<PERSON><PERSON> \",\n            \"<PERSON><PERSON>wani \",\n            \"Hital Rasiklal Meswani \"\n        ],\n        \"annual_turnover\": \"Slab: Rs. 500 Cr. and above\",\n        \"annual_turnover_fy\": \"2023-2024\",\n        \"percentage_in_cash_fy\": \"2024-2025\",\n        \"percentage_in_cash\": \"Slab: 20% & above\",\n        \"aadhaar_validation\": \"Yes\",\n        \"aadhaar_validation_date\": \"2024-02-17\",\n        \"address_details\": {},\n        \"liability_percentage_details\": {},\n        \"less_info\": false,\n        \"einvoice_status\": true,\n        \"client_id\": \"corporate_gstin_advanced_komqMYgetsZMioGztvkQ\",\n        \"gstin\": \"27**********2Z6\",\n        \"pan_number\": \"**********\",\n        \"fy\": null,\n        \"business_name\": \"Reliance Industries Limited\",\n        \"legal_name\": \"Reliance Industries Limited\",\n        \"center_jurisdiction\": \"State - CBIC,Zone - MUMBAI,Commissionerate - BELAPUR,Division - DIVISION IV,Range - RANGE-IV (Jurisdictional Office)\",\n        \"state_jurisdiction\": \"State - Maharashtra,Zone - RAIGAD,Division - RAIGAD-NORTH,Charge - URAN_701\",\n        \"date_of_registration\": \"2017-06-28\",\n        \"constitution_of_business\": \"Public Limited Company\",\n        \"taxpayer_type\": \"Regular\",\n        \"gstin_status\": \"Active\",\n        \"date_of_cancellation\": \"1800-01-01\",\n        \"field_visit_conducted\": \"No\",\n        \"nature_bus_activities\": [\n            \"Recipient of Goods or Services\",\n            \"Supplier of Services\",\n            \"Export\",\n            \"Office / Sale Office\",\n            \"Import\"\n        ],\n        \"nature_of_core_business_activity_code\": \"SPO\",\n        \"nature_of_core_business_activity_description\": \"Service Provider and Others\",\n        \"filing_status\": [],\n        \"address\": null,\n        \"hsn_info\": {},\n        \"filing_frequency\": []\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": null,\n    \"message_code\": \"success\"\n}"}]}, {"name": "D GST fetch PDF report", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n//    \"client_id\": \"corporate_gstin_advanced_ElpdsFtnwJvamHjoNvrK\" // get from GST fetch details API\n//    OR \n   \"id_number\": \"27**********2Z6\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/corporate/gstin-pdf-report", "description": "Generated from cURL: curl --location 'https://sandbox.surepass.io/api/v1/corporate/gstin-pdf-report' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n   \"client_id\": \"GSTIN_CLIENT_ID\"\n}'"}, "response": [{"name": "GST fetch PDF report", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n//    \"client_id\": \"corporate_gstin_advanced_ElpdsFtnwJvamHjoNvrK\" // get from GST fetch details API\n//    OR \n   \"id_number\": \"27**********2Z6\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/corporate/gstin-pdf-report"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 23 May 2025 06:16:59 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "609"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": "gstin_pdf_report_SpFyMgblHUnfynkyvpzC"}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"gstin_pdf_report_SpFyMgblHUnfynkyvpzC\",\n        \"gstin\": \"27**********2Z6\",\n        \"pdf_report\": \"https://aadhaar-kyc-docs.s3.amazonaws.com/sumit.arya/pdf_report_gstin/gstin_pdf_report_SpFyMgblHUnfynkyvpzC/pdf_report_gstin_1747981019495292.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAY5K3QRM5FYWPQJEB%2F20250523%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250523T061659Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=f3e8561994dd97d2e92dc165cfd2aaccbd592166513e343948242aee4b91c262\"\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": \"Success\",\n    \"message_code\": \"success\"\n}"}]}, {"name": "<PERSON> <PERSON><PERSON><PERSON>ar send OTP", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "// https://sandbox.surepass.io/api/v1/aadhaar/eaadhaar/generate-otp\n// https://sandbox.surepass.io/api/v1/aadhaar-v2/generate-otp\n{\n    \"id_number\": \"************\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/aadhaar-v2/generate-otp", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/eaadhaar/generate-otp' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n    \"id_number\": \"************\"\n}'\n"}, "response": [{"name": "<PERSON><PERSON><PERSON><PERSON> send O<PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"id_number\": \"************\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/aadhaar/eaadhaar/generate-otp"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Wed, 14 May 2025 11:38:35 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "204"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": "eaadhaar_JkHErhDkJMwjoXfpnHYk"}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"eaadhaar_JkHErhDkJMwjoXfpnHYk\",\n        \"otp_sent\": true,\n        \"if_number\": true,\n        \"valid_aadhaar\": true\n    },\n    \"status_code\": 200,\n    \"message_code\": \"success\",\n    \"message\": \"OTP Sent\",\n    \"success\": true\n}"}]}, {"name": "<PERSON> <PERSON><PERSON><PERSON><PERSON> fetch details", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": \"eaadhaar_mFaLycxnwspVjqhdXaLH\",\n    \"otp\": \"937563\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/aadhaar/eaadhaar/submit-otp", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/eaadhaar/submit-otp' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n    \"client_id\": \"eaadhaar_TttxzXfHokcKiIUeeoYh\",\n    \"otp\": \"258602\"\n}'\n"}, "response": [{"name": "<PERSON><PERSON><PERSON><PERSON> fetch details", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "// https://sandbox.surepass.io/api/v1/aadhaar/eaadhaar/submit-otp\n// https://sandbox.surepass.io/api/v1/aadhaar-v2/submit-otp\n\n{\n    \"client_id\": \"aadhaar_v2_uxwosqIlazvwSooNIkPs\",\n    \"otp\": \"616242\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/aadhaar-v2/submit-otp"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 22 May 2025 12:47:44 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "8441"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": "aadhaar_v2_uxwosqIlazvwSooNIkPs"}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"aadhaar_v2_uxwosqIlazvwSooNIkPs\",\n        \"full_name\": \"Anubhav Baweja\",\n        \"aadhaar_number\": \"XXXXXXXX3987\",\n        \"dob\": \"1996-09-21\",\n        \"gender\": \"M\",\n        \"address\": {\n            \"country\": \"India\",\n            \"dist\": \"Panipat\",\n            \"state\": \"Haryana\",\n            \"po\": \"Panipat\",\n            \"loc\": \"Panipat\",\n            \"vtc\": \"Panipat\",\n            \"subdist\": \"\",\n            \"street\": \"Sector-1A\",\n            \"house\": \"Flat No. T5-02/11, Emperium Happy Homes\",\n            \"landmark\": \"Opp. Virat Nagar\"\n        },\n        \"face_status\": false,\n        \"face_score\": -1,\n        \"zip\": \"132103\",\n        \"profile_image\": \"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\",\n        \"has_image\": true,\n        \"email_hash\": \"\",\n        \"mobile_hash\": \"da8804018d5b9f1780dd649db54379ce8050af174a9f413bc60072735585c284\",\n        \"raw_xml\": \"https://aadhaar-kyc-docs.s3.amazonaws.com/sumit.arya/aadhaar_xml/398720250522181743854/398720250522181743854-2025-05-22-124744640756.xml?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAY5K3QRM5FYWPQJEB%2F20250522%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250522T124744Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=48d4be5a30e469cbb5fbd2bc96c7620e3772cb4219287d88b2faafa8bd3bb91d\",\n        \"zip_data\": \"https://aadhaar-kyc-docs.s3.amazonaws.com/sumit.arya/aadhaar_xml/398720250522181743854/398720250522181743854-2025-05-22-124744562793.zip?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAY5K3QRM5FYWPQJEB%2F20250522%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250522T124744Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=630e34e1169564ef0cdf5b3f4693f41b143c19a88bd526d8bc0547c83748c3dc\",\n        \"care_of\": \"S/O Tara Chand Baweja\",\n        \"share_code\": \"6260\",\n        \"mobile_verified\": false,\n        \"reference_id\": \"398720250522181743854\",\n        \"aadhaar_pdf\": null,\n        \"status\": \"success_aadhaar\",\n        \"uniqueness_id\": \"5d3607da34e52bfc6122da3743287449f2fb8963bf44bdf53646de8e9096ca1d\"\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": null,\n    \"message_code\": \"success\"\n}"}]}, {"name": "<PERSON> <PERSON><PERSON><PERSON> fetch PDF", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": \"aadhaar_v2_uxwosqIlazvwSooNIkPs\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/aadhaar-v2/generate-pdf", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/eaadhaar/submit-otp' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data '{\n    \"client_id\": \"eaadhaar_TttxzXfHokcKiIUeeoYh\",\n    \"otp\": \"258602\"\n}'\n"}, "response": []}, {"name": "<PERSON><PERSON><PERSON><PERSON> fetch details from file (OCR)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": "https://sandbox.surepass.io/api/v1/ocr/aadhaar", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/ocr/aadhaar' \\\n--form 'file=@\"/path/to/file\"'\n"}, "response": []}, {"name": "Digilocker initialize", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"data\": {\n        \"prefill_options\": {\n            \"full_name\": \"Anubhav Baweja\",\n            \"mobile_number\": \"9034484773\",\n            \"user_email\": \"<EMAIL>\"\n        },\n        \"expiry_minutes\": 20,\n        \"send_sms\": false,\n        \"send_email\": false,\n        \"verify_phone\": false,\n        \"verify_email\": false,\n        \"signup_flow\": false,\n        \"redirect_url\": \"https://google.com\",\n        \"state\": \"test\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/digilocker/initialize", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/digilocker/initialize' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN' \\\n--data-raw '{\n    \"data\": {\n        \"prefill_options\": {\n            \"full_name\": \"<PERSON><PERSON>\",\n            \"mobile_number\": \"9999999999\",\n            \"user_email\": \"<EMAIL>\"\n        },\n        \"expiry_minutes\": 10,\n        \"send_sms\": false,\n        \"send_email\": false,\n        \"verify_phone\": false,\n        \"verify_email\": false,\n        \"signup_flow\": false,\n        \"redirect_url\": \"https://google.com\",\n        \"state\": \"test\"\n    }\n}'\n"}, "response": [{"name": "Digilocker initialize", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "body": {"mode": "raw", "raw": "{\n    \"data\": {\n        \"prefill_options\": {\n            // \"full_name\": \"Anubhav Baweja\",\n            \"mobile_number\": \"9034484773\"\n            // \"user_email\": \"<EMAIL>\"\n        },\n        \"expiry_minutes\": 20,\n        \"send_sms\": false,\n        \"send_email\": false,\n        \"verify_phone\": false,\n        \"verify_email\": false,\n        \"signup_flow\": false,\n        \"redirect_url\": \"https://google.com\",\n        \"state\": \"test\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/digilocker/initialize"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 22 May 2025 10:56:42 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "573"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": ""}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"digilocker_wTZJafwDbMfHoZMZalYX\",\n        \"token\": \".eJyrVkrOyUzNK4nPTFGyUkrJTM_MyU_OTi2KLw-J8kpMK3dJ8k3zyI_yjUrMiYxQ0lFKTyxJLU-sBKotTsxLScqvAIqVVBakomhWqgUAVrQeug.aC8C6w.xLq1wR56fQidPOrak44X05Ji9cc\",\n        \"url\": \"https://digilocker-sdk.notbot.in/?gateway=sandbox&type=digilocker&token=.eJyrVkrOyUzNK4nPTFGyUkrJTM_MyU_OTi2KLw-J8kpMK3dJ8k3zyI_yjUrMiYxQ0lFKTyxJLU-sBKotTsxLScqvAIqVVBakomhWqgUAVrQeug.aC8C6w.xLq1wR56fQidPOrak44X05Ji9cc&auth_type=web\",\n        \"expiry_seconds\": 1200\n    },\n    \"status_code\": 200,\n    \"message_code\": \"success\",\n    \"message\": \"Success\",\n    \"success\": true\n}"}]}, {"name": "Digilocker fetch documents", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "url": "https://sandbox.surepass.io/api/v1/digilocker/list-documents/digilocker_QajQecWjnaUmFthHLwiq", "description": "Generated from cURL: curl --location -g 'https://kyc-api.surepass.io/api/v1/digilocker/list-documents/{{digilocker_client_id}}' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN'\n"}, "response": [{"name": "Digilocker Fetch documents", "originalRequest": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "url": "https://sandbox.surepass.io/api/v1/digilocker/list-documents/digilocker_wTZJafwDbMfHoZMZalYX"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Thu, 22 May 2025 11:00:18 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "711"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": ""}], "cookie": [], "body": "{\n    \"data\": {\n        \"documents\": [\n            {\n                \"file_id\": \"digilocker_file_BgJTjWlvJaAfzhyoJsRc\",\n                \"name\": \"Aadhaar Card\",\n                \"doc_type\": \"ADHAR\",\n                \"downloaded\": true,\n                \"issuer\": \"Unique Identification Authority of India (UIDAI)\",\n                \"description\": \"Aadhaar Card\"\n            },\n            {\n                \"file_id\": \"digilocker_file_UEwuwGHnnpcrGxlsEwoo\",\n                \"name\": \"PAN Verification Record\",\n                \"doc_type\": \"PANCR\",\n                \"downloaded\": true,\n                \"issuer\": \"Income Tax Department\",\n                \"description\": \"PAN Verification Record\"\n            },\n            {\n                \"file_id\": \"aadhaar\",\n                \"name\": \"Aadhaar Card\",\n                \"doc_type\": \"ADHAR\",\n                \"downloaded\": true,\n                \"issuer\": \"Unique Identification Authority of India (UIDAI)\",\n                \"description\": \"Aadhaar Card\"\n            }\n        ]\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": \"Success\",\n    \"message_code\": \"success\"\n}"}]}, {"name": "Digilocker download document", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg"}], "url": "https://sandbox.surepass.io/api/v1/digilocker/download-document/digilocker_QajQecWjnaUmFthHLwiq/digilocker_file_gAzMbtYppQUiakqyykAh", "description": "Generated from cURL: curl --location -g 'https://kyc-api.surepass.io/api/v1/digilocker/download-document/{{digilocker_client_id}}/{{digi_file_id_0}}' \\\n--header 'Content-Type: application/json' \\\n--header 'Authorization: Bearer TOKEN'\n"}, "response": []}, {"name": "E-sign initialize", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"pdf_pre_uploaded\": true,\n    \"sign_type\": \"suresign\",\n    \"config\": {\n        \"auth_mode\": \"1\",\n        \"reason\": \"General- Agreement\"\n    },\n    \"prefill_options\": {\n        \"full_name\": \"<PERSON><PERSON>\",\n        \"mobile_number\": \"9876543210\",\n        \"user_email\": \"<EMAIL>\"\n    },\n            \"positions\": {\n            \"1\": [\n                {\n                    \"x\": 10,\n                    \"y\": 20\n                }\n            ],\n            \"2\": [\n                {\n                    \"x\": 0,\n                    \"y\": 0\n                }\n            ]\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/esign/initialize", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/esign/initialize' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    \"pdf_pre_uploaded\": true,\n    \"sign_type\": \"suresign\",\n    \"config\": {\n        \"auth_mode\": \"1\",\n        \"reason\": \"General- Agreement\"\n    },\n    \"prefill_options\": {\n        \"full_name\": \"<PERSON><PERSON> Bhaiya\",\n        \"mobile_number\": \"9876543210\",\n        \"user_email\": \"<EMAIL>\"\n    },\n            \"positions\": {\n            \"1\": [\n                {\n                    \"x\": 10,\n                    \"y\": 20\n                }\n            ],\n            \"2\": [\n                {\n                    \"x\": 0,\n                    \"y\": 0\n                }\n            ]\n        }\n}'\n"}, "response": [{"name": "E-sign initialize", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"pdf_pre_uploaded\": true,\n    \"sign_type\": \"suresign\",\n    \"config\": {\n        \"auth_mode\": \"1\",\n        \"reason\": \"General- Agreement\"\n    },\n    \"prefill_options\": {\n        // \"full_name\": \"Anub<PERSON>v Baweja\",\n        // \"mobile_number\": \"9034484773\"\n        // \"user_email\": \"<EMAIL>\"\n    },\n            \"positions\": {\n            \"1\": [\n                {\n                    \"x\": 10,\n                    \"y\": 20\n                }\n            ],\n            \"2\": [\n                {\n                    \"x\": 0,\n                    \"y\": 0\n                }\n            ]\n        }\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/esign/initialize"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 23 May 2025 05:50:35 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "1018"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self' *.surepass.io; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.surepass.io blob:;"}, {"key": "Permissions-Policy", "value": "accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(), usb=(), web-share=(), xr-spatial-tracking=(), clipboard-read=(), clipboard-write=(), gamepad=(), speaker-selection=(), conversion-measurement=(), focus-without-user-activation=(), hid=(), idle-detection=(), interest-cohort=(), serial=(), sync-script=(), trust-token-redemption=(), window-placement=(), vertical-scroll=()"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-Permitted-Cross-Domain-Policies", "value": "none"}, {"key": "X-Client-ID", "value": ""}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"esign_fwQujmuzwkgqakYsSxsD\",\n        \"group_id\": null,\n        \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Nzk3OTQzNSwianRpIjoiY2RmNGU1MGMtNTJmNC00ZjY5LTgwNWYtMmE5OTdkMTRjOTk2IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoiZXNpZ25fZndRdWptdXp3a2dxYWtZc1N4c0QiLCJuYmYiOjE3NDc5Nzk0MzUsImV4cCI6MTc0ODU4NDIzNSwiZW1haWwiOiIiLCJ0ZW5hbnRfaWQiOiIiLCJ1c2VyX2NsYWltcyI6eyJnYXRld2F5Ijoic2FuZGJveCJ9fQ.UBZM3xWBoyH8IoqyDdj6hYUar8hUgwsmIWl5E1dz17Q\",\n        \"url\": \"https://esign-client.surepass.io/?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc0Nzk3OTQzNSwianRpIjoiY2RmNGU1MGMtNTJmNC00ZjY5LTgwNWYtMmE5OTdkMTRjOTk2IiwidHlwZSI6ImFjY2VzcyIsImlkZW50aXR5IjoiZXNpZ25fZndRdWptdXp3a2dxYWtZc1N4c0QiLCJuYmYiOjE3NDc5Nzk0MzUsImV4cCI6MTc0ODU4NDIzNSwiZW1haWwiOiIiLCJ0ZW5hbnRfaWQiOiIiLCJ1c2VyX2NsYWltcyI6eyJnYXRld2F5Ijoic2FuZGJveCJ9fQ.UBZM3xWBoyH8IoqyDdj6hYUar8hUgwsmIWl5E1dz17Q\"\n    },\n    \"status_code\": 200,\n    \"message_code\": \"success\",\n    \"message\": \"User initialized successfully\",\n    \"success\": true\n}"}]}, {"name": "E-sign upload PDF", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": \"esign_jeiitppqynrZRvwDiZpD\",\n    \"link\": \"https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/esign/upload-pdf", "description": "Generated from cURL: curl --location 'https://kyc-api.surepass.io/api/v1/esign/upload-pdf' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"client_id\": \"{{client_id}}\",\n    \"link\": \"https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf\"\n}'\n"}, "response": [{"name": "E-sign upload PDF", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": \"esign_fwQujmuzwkgqakYsSxsD\",\n    \"link\": \"https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sandbox.surepass.io/api/v1/esign/upload-pdf"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 23 May 2025 05:52:36 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "115"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": ""}], "cookie": [], "body": "{\n    \"data\": {\n        \"uploaded\": true\n    },\n    \"status_code\": 200,\n    \"message_code\": \"success\",\n    \"message\": \"Success\",\n    \"success\": true\n}"}]}, {"name": "E-sign fetch status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "url": "https://sandbox.surepass.io/api/v1/esign/status/esign_jeiitppqynrZRvwDiZpD", "description": "Generated from cURL: curl --location -g 'https://kyc-api.surepass.io/api/v1/esign/status/{{client_id}}'"}, "response": [{"name": "E-sign Fetch status", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "url": "https://sandbox.surepass.io/api/v1/esign/status/esign_fwQujmuzwkgqakYsSxsD"}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Date", "value": "Fri, 23 May 2025 05:55:02 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "237"}, {"key": "Connection", "value": "keep-alive"}, {"key": "X-Client-ID", "value": ""}], "cookie": [], "body": "{\n    \"data\": {\n        \"client_id\": \"esign_fwQujmuzwkgqakYsSxsD\",\n        \"status\": \"otp_sent\",\n        \"completed\": false,\n        \"esign_error\": false,\n        \"error_message_from_nsdl\": null\n    },\n    \"status_code\": 200,\n    \"success\": true,\n    \"message\": \"Success\",\n    \"message_code\": \"success\"\n}"}]}, {"name": "E-sign fetch audit-trail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0n2q7OQ1baogPuUl0-WPDiKF2ZCc8VBxZ1DaByoYeHg", "type": "text"}], "url": "https://sandbox.surepass.io/api/v1/esign/audit-trail/esign_jeiitppqynrZRvwDiZpD", "description": "Generated from cURL: curl --location -g 'https://kyc-api.surepass.io/api/v1/esign/audit-trail/{{client_id}}'"}, "response": []}]}]}