version: '3'

volumes:
  account_api_v2_local_mysql_data: {}

services:
  app:
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    image: myop/account_api_v2_local:latest
    depends_on:
      - redis
      - mysql
    volumes:
      - .:/app
    ports:
      - "${DOCKER_PORT:-8000}:8000"
    command: /start

  redis:
    image: redis:5.0
    ports:
      - "6379:6379"
    expose:
      - 6379

  celery:
    image: myop/account_api_v2_local:latest
    volumes:
      - .:/app
    ports: []
    command: celery -A config.celery.app worker --loglevel=DEBUG
    depends_on:
      - redis

  mysql:
    image: mysql:8.0
    ports:
      - "${DOCKER_MYSQL_PORT:-3306}:3306"
    expose:
      - "3306"
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: test
      MYSQL_PASS: test
      MYSQL_PASSWORD: test
      MYSQL_DATABASE: account_db
    volumes:
      - account_api_v2_local_mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      retries: 5
      start_period: 30s
