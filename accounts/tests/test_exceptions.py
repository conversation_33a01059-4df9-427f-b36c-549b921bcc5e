import pytest
from rest_framework import status

from accounts.exceptions import (
    BaseException,
    ServerError,
    OTPMaxAttemptException,
    OTPException,
)


class TestServerError:
    """Test cases for ServerError exception."""

    def test_inheritance(self):
        """Test that Server<PERSON>rror inherits from BaseException."""
        exception = ServerError()
        assert isinstance(exception, BaseException)

    def test_default_message(self):
        """Test ServerError default message."""
        exception = ServerError()
        assert exception.message == "Server Error"

    def test_custom_message(self):
        """Test ServerError with custom message."""
        custom_message = "Custom server error message"
        exception = ServerError(custom_message)
        assert str(exception) == custom_message

    def test_string_representation(self):
        """Test string representation of ServerError."""
        exception = ServerError("Test server error")
        assert str(exception) == "Test server error"


class TestOTPMaxAttemptException:
    """Test cases for OTPMaxAttemptException."""

    def test_inheritance(self):
        """Test that OTPMaxAttemptException inherits from BaseException."""
        exception = OTPMaxAttemptException()
        assert isinstance(exception, BaseException)

    def test_http_status_code(self):
        """Test OTPMaxAttemptException HTTP status code."""
        exception = OTPMaxAttemptException()
        assert exception.http_status_code == status.HTTP_429_TOO_MANY_REQUESTS

    def test_default_message(self):
        """Test OTPMaxAttemptException default message."""
        exception = OTPMaxAttemptException()
        assert exception.message == "Maximum OTP attempts exceeded."

    def test_custom_message(self):
        """Test OTPMaxAttemptException with custom message."""
        custom_message = "Custom OTP max attempt message"
        exception = OTPMaxAttemptException(custom_message)
        assert str(exception) == custom_message

    def test_string_representation(self):
        """Test string representation of OTPMaxAttemptException."""
        exception = OTPMaxAttemptException("Test OTP max attempt")
        assert str(exception) == "Test OTP max attempt"

    def test_status_code_value(self):
        """Test that status code is 429."""
        exception = OTPMaxAttemptException()
        assert exception.http_status_code == 429


class TestOTPException:
    """Test cases for OTPException."""

    def test_inheritance(self):
        """Test that OTPException inherits from BaseException."""
        exception = OTPException()
        assert isinstance(exception, BaseException)

    def test_http_status_code(self):
        """Test OTPException HTTP status code."""
        exception = OTPException()
        assert exception.http_status_code == status.HTTP_424_FAILED_DEPENDENCY

    def test_default_message(self):
        """Test OTPException default message."""
        exception = OTPException()
        assert exception.message == "OTP Exception."

    def test_custom_message(self):
        """Test OTPException with custom message."""
        custom_message = "Custom OTP exception message"
        exception = OTPException(custom_message)
        assert str(exception) == custom_message

    def test_string_representation(self):
        """Test string representation of OTPException."""
        exception = OTPException("Test OTP exception")
        assert str(exception) == "Test OTP exception"

    def test_status_code_value(self):
        """Test that status code is 424."""
        exception = OTPException()
        assert exception.http_status_code == 424


class TestExceptionInteraction:
    """Test cases for exception interactions."""

    def test_exception_hierarchy(self):
        """Test exception hierarchy relationships."""
        # All should inherit from BaseException
        assert issubclass(ServerError, BaseException)
        assert issubclass(OTPMaxAttemptException, BaseException)
        assert issubclass(OTPException, BaseException)

    def test_different_status_codes(self):
        """Test that different exceptions have different status codes."""
        otp_max_exception = OTPMaxAttemptException()
        otp_exception = OTPException()

        assert (
            otp_max_exception.http_status_code != otp_exception.http_status_code
        )
        assert otp_max_exception.http_status_code == 429
        assert otp_exception.http_status_code == 424

    def test_exception_messages_are_different(self):
        """Test that different exceptions have different default messages."""
        server_error = ServerError()
        otp_max_exception = OTPMaxAttemptException()
        otp_exception = OTPException()

        assert server_error.message != otp_max_exception.message
        assert server_error.message != otp_exception.message
        assert otp_max_exception.message != otp_exception.message

    def test_exception_raising(self):
        """Test that exceptions can be raised and caught properly."""
        with pytest.raises(ServerError):
            raise ServerError("Test server error")

        with pytest.raises(OTPMaxAttemptException):
            raise OTPMaxAttemptException("Test OTP max attempt")

        with pytest.raises(OTPException):
            raise OTPException("Test OTP exception")

    def test_exception_catching_by_base_class(self):
        """Test that exceptions can be caught by their base class."""
        with pytest.raises(BaseException):
            raise ServerError("Test server error")

        with pytest.raises(BaseException):
            raise OTPMaxAttemptException("Test OTP max attempt")

        with pytest.raises(BaseException):
            raise OTPException("Test OTP exception")

    def test_exception_attributes_inheritance(self):
        """Test that exceptions inherit attributes from BaseException."""
        # These should inherit from BaseException
        server_error = ServerError()
        otp_max_exception = OTPMaxAttemptException()
        otp_exception = OTPException()

        # Test that they inherit from BaseException
        assert isinstance(server_error, BaseException)
        assert isinstance(otp_max_exception, BaseException)
        assert isinstance(otp_exception, BaseException)

    def test_exception_with_args_and_kwargs(self):
        """Test exceptions with various arguments."""
        # Test with positional arguments
        exception1 = OTPException("Error message")
        assert str(exception1) == "Error message"

        # Test with no arguments
        exception2 = OTPException()
        assert exception2.message == "OTP Exception."

        # Test with multiple arguments
        exception3 = OTPMaxAttemptException("Error", "Additional info")
        assert "Error" in str(exception3)
