import pytest

from accounts import error_codes


class TestErrorCodes:
    """Test cases for error codes."""

    def test_gst_verify_otp_send_max_attempt_exceeded_code(self):
        """Test GST verify OTP send max attempt exceeded error code."""
        assert error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED == "ACC_0501"

    def test_gst_verify_otp_send_failed_dependency_code(self):
        """Test GST verify OTP send failed dependency error code."""
        assert error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY == "ACC_0502"

    def test_error_codes_are_strings(self):
        """Test that error codes are strings."""
        assert isinstance(error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED, str)
        assert isinstance(error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY, str)

    def test_error_codes_follow_pattern(self):
        """Test that error codes follow the expected pattern."""
        # Should start with ACC_ and have numeric suffix
        assert error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED.startswith("ACC_")
        assert error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY.startswith("ACC_")
        
        # Should have numeric part after ACC_
        code1_suffix = error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED.split("_")[1]
        code2_suffix = error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY.split("_")[1]
        
        assert code1_suffix.isdigit()
        assert code2_suffix.isdigit()

    def test_error_codes_are_unique(self):
        """Test that error codes are unique."""
        assert (
            error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED 
            != error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY
        )

    def test_error_codes_length(self):
        """Test that error codes have expected length."""
        # ACC_XXXX format should be 8 characters
        assert len(error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED) == 8
        assert len(error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY) == 8

    def test_error_codes_constants_exist(self):
        """Test that the error code constants exist in the module."""
        assert hasattr(error_codes, 'GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED')
        assert hasattr(error_codes, 'GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY')

    def test_error_codes_not_empty(self):
        """Test that error codes are not empty."""
        assert error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED
        assert error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY

    def test_error_codes_immutable(self):
        """Test that error codes are effectively immutable (strings)."""
        # Strings are immutable in Python, so this tests the type
        original_code = error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED
        
        # This should not change the original
        modified = original_code.replace("ACC", "XYZ")
        
        assert error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED == original_code
        assert modified != original_code

    def test_existing_errors_list_exists(self):
        """Test that the existing ERRORS list still exists."""
        assert hasattr(error_codes, 'ERRORS')
        assert isinstance(error_codes.ERRORS, list)

    def test_error_codes_numeric_sequence(self):
        """Test that new error codes follow numeric sequence."""
        code1_num = int(error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED.split("_")[1])
        code2_num = int(error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY.split("_")[1])
        
        # Should be consecutive numbers
        assert abs(code1_num - code2_num) == 1

    def test_error_codes_in_500_range(self):
        """Test that new error codes are in the 500 range."""
        code1_num = int(error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED.split("_")[1])
        code2_num = int(error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY.split("_")[1])
        
        # Should be in 500 range (501, 502)
        assert 500 <= code1_num < 600
        assert 500 <= code2_num < 600

    def test_error_codes_specific_values(self):
        """Test specific values of the new error codes."""
        assert error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED == "ACC_0501"
        assert error_codes.GST_VERIFY_OTP_SEND_FAILED_DEPENDENCY == "ACC_0502"
