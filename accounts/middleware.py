import uuid
import logging

from rest_framework.response import Response

from accounts.error_codes import SERVER_ERROR
from config.renderer import MyOperatorRenderer

logger = logging.getLogger(__name__)


class MyOperatorMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if "HTTP_X_MYOP_TRACE_ID" not in request.META:
            request.META["HTTP_X_MYOP_TRACE_ID"] = uuid.uuid4()
        # configure centrallog with X-MYOP-TRACE-ID
        self._set_logging_uid(request.META["HTTP_X_MYOP_TRACE_ID"])
        return self.get_response(request)

    def process_template_response(self, request, response):
        response.setdefault(
            "X-MYOP-TRACE-ID", request.META["HTTP_X_MYOP_TRACE_ID"]
        )
        return response

    def _set_logging_uid(self, uid):
        try:
            from myoperator.centrallog import config

            config.configure(uid=uid)
        except ImportError:
            pass

    def _get_statuscode_from_exception(self, exception):
        statuscode_exceptions = {
            TypeError: 500,
            KeyError: 500,
        }

        return statuscode_exceptions.get(exception.__class__, 500)

    def process_exception(self, request, exception):
        logger.critical(exception, exc_info=True)
        status_code = self._get_statuscode_from_exception(exception)
        response = Response(
            {
                "errors": {"detail": str(exception)},
                "code": SERVER_ERROR,
            },
            status_code,
        )
        response.accepted_media_type = "application/json"
        response.renderer_context = {}

        response.accepted_renderer = MyOperatorRenderer()
        return response
