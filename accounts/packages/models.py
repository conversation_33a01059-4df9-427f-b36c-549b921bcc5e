from django.db import models

from accounts.discounts.models import Discounts
from accounts.products.models import (
    ProductFeatureProperties,
    ProductFeatures,
    Products,
)
from accounts.users.models import Groups
from accounts.global_packages.models import GlobalPackages, PackageCategories
from accounts.utils.common import uuid
from accounts.packages import constants
from accounts.packages.enums import (
    PackageFeatureEventTypeEnums,
    PackageStatusEnum,
)


class PackageCustoms(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    parent_package = models.ForeignKey(
        GlobalPackages, on_delete=models.DO_NOTHING
    )
    package_rent = models.DecimalField(max_digits=10, decimal_places=2)
    additional_rent = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    additional_cost = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    additional_cost_description = models.CharField(
        max_length=250, default="0.00"
    )
    payment_cycle = models.IntegerField()
    features = models.TextField()
    free = models.TextField()
    property_id = models.TextField(blank=True)
    deviation = models.TextField()
    created_by = models.CharField(max_length=36)
    accept_status = models.SmallIntegerField(default=0)
    accepted_by = models.CharField(max_length=36)
    custom_code = models.CharField(max_length=20)
    discount_id = models.IntegerField(default=None, null=True)
    discount_type = models.CharField(max_length=50, default="0")
    discount_data = models.TextField(default=None, blank=True)
    business_name = models.CharField(max_length=255, null=True)
    is_pack_changed = models.IntegerField(default=0)
    comment_to_customer = models.TextField(blank=True)
    pdf_url = models.CharField(max_length=250, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_customs"


class Packages(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    global_package = models.ForeignKey(
        GlobalPackages, on_delete=models.DO_NOTHING
    )
    product = models.ForeignKey(
        Products,
        on_delete=models.DO_NOTHING,
    )
    name = models.CharField(max_length=40)
    package_type = models.CharField(
        max_length=6,
        choices=[
            ("add-on", "add-on"),
            ("main", "main"),
        ],
    )
    rent_per_month = models.DecimalField(max_digits=10, decimal_places=3)
    renew_cycle = models.PositiveIntegerField()
    package_custom = models.ForeignKey(
        PackageCustoms, on_delete=models.DO_NOTHING, null=True, default=None
    )
    is_public = models.BooleanField(default=True)
    code = models.CharField(max_length=8)
    ocs_flag = models.SmallIntegerField(
        choices=[
            (0, "account only"),
            (1, "ocs only"),
            (2, "myoperator and account both"),
            (3, "non"),
        ]
    )
    package_for = models.CharField(
        max_length=3,
        choices=[
            ("vn", "virtual number"),
            ("tn", "tollfree number"),
            ("mt", "mobile sync"),
            ("hn", "Heyo number"),
        ],
    )
    package_category = models.ForeignKey(
        PackageCategories, on_delete=models.CASCADE
    )
    package_number = models.CharField(max_length=4)
    discount = models.ForeignKey(
        Discounts, on_delete=models.SET_NULL, null=True
    )
    description = models.TextField(blank=True, null=True)
    status = models.SmallIntegerField(
        choices=PackageStatusEnum.choices(),
        default=PackageStatusEnum.ACTIVE.value,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "packages"
        indexes = [
            models.Index(fields=["product_id"]),
            models.Index(fields=["name"]),
            models.Index(fields=["package_type"]),
            models.Index(fields=["rent_per_month"]),
            models.Index(fields=["is_public"]),
            models.Index(fields=["code"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ocs_flag"]),
            models.Index(fields=["package_for"]),
            models.Index(fields=["package_custom_id"]),
        ]
        ordering = ["created"]


class PackageFeatures(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    package = models.ForeignKey(Packages, on_delete=models.CASCADE)
    product_feature = models.ForeignKey(
        ProductFeatures, on_delete=models.CASCADE
    )
    product_feature_property = models.ForeignKey(
        ProductFeatureProperties, on_delete=models.SET_NULL, null=True
    )
    free_unit = models.IntegerField(default=0)
    additional = models.IntegerField(default=0)
    rent_per_month = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.SmallIntegerField(
        choices=[(0, "disabled"), (1, "enabled"), (2, "deleted")],
        default=constants.PACKAGE_FEATURE_ENABLED,
    )
    last_disabled_date = models.DateTimeField(null=True)
    is_customer_cancelled = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_features"
        indexes = [
            models.Index(fields=["package_id"]),
            models.Index(fields=["product_feature_id"]),
            models.Index(fields=["product_feature_property_id"]),
            models.Index(fields=["status"]),
            models.Index(fields=["last_disabled_date"]),
        ]
        ordering = ["created"]

    def get_resource_key(self):
        """
        Returns the resource key for the package feature.
        """
        resource_key = self.product_feature.resource_key
        if self.product_feature_property_id:
            resource_key = f"{resource_key}{self.product_feature_property.post_fix_resource}"
        return resource_key

    def get_memcache_key(self):
        """
        Returns the memcache key for the package feature.
        """
        memcache_key = self.product_feature.memcache_key
        if self.product_feature_property_id:
            memcache_key = f"{memcache_key}{self.product_feature_property.post_fix_memcache}"
        return memcache_key


class PackageFeatureRates(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    package_feature = models.ForeignKey(
        PackageFeatures, on_delete=models.CASCADE, related_name="rate_slabs"
    )
    min = models.IntegerField()
    max = models.IntegerField()
    rate = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.SmallIntegerField(
        default=constants.PACKAGE_FEATURE_RATE_ACTIVE
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_feature_rates"
        indexes = [models.Index(fields=["package_feature_id", "status"])]
        ordering = ["created"]


class PackageFeaturePromos(models.Model):
    from accounts.services.models import Services

    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    package_feature = models.ForeignKey(
        PackageFeatures, on_delete=models.CASCADE
    )
    activation_date = models.DateTimeField()
    free_units = models.IntegerField(default=0)
    months = models.SmallIntegerField(default=0)
    status = models.SmallIntegerField(
        default=constants.PACKAGE_FEATURE_PROMO_ACTIVE,
        help_text="1 => promo_active, 0 => promo_expired",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_feature_promo"
        indexes = [
            models.Index(fields=["service_id"]),
            models.Index(fields=["package_feature_id"]),
        ]
        ordering = ["created"]


class PlanShares(models.Model):
    id = models.AutoField(primary_key=True)
    package = models.ForeignKey(Packages, on_delete=models.CASCADE)
    business_type = models.CharField(max_length=200)
    from_emails = models.TextField()
    to_emails = models.TextField()
    cc_emails = models.TextField()
    bcc_emails = models.TextField()
    email_body = models.TextField()
    attachments = models.CharField(max_length=250)
    shared_by_user = models.CharField(max_length=36)
    proposal = models.CharField(max_length=50)
    shared_by_dept = models.CharField(max_length=36)
    type = models.SmallIntegerField(
        default=1, help_text="1-Custom package proposal, 2- Readymade proposals"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "plan_shares"


class ProposalTestimonials(models.Model):
    id = models.AutoField(primary_key=True)
    package_category = models.ForeignKey(
        PackageCategories, on_delete=models.CASCADE
    )
    proposal_type = models.CharField(max_length=100)
    name = models.CharField(max_length=250)
    text = models.TextField()
    logo = models.CharField(max_length=250)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "proposal_testimonials"


class PackageFeatureHistory(models.Model):

    id = models.AutoField(primary_key=True)
    package_feature = models.ForeignKey(
        PackageFeatures, on_delete=models.DO_NOTHING
    )
    event_type = models.SmallIntegerField(
        choices=PackageFeatureEventTypeEnums.choices()
    )
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_feature_history"
