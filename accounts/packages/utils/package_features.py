import logging
from accounts.packages.models import PackageFeatures, PackageFeatureHistory
from django.db.models.query import QuerySet
from accounts.packages import constants
from typing import Optional
from accounts.packages.enums import PackageFeatureEventTypeEnums
from accounts.products.enums import ProductFeatureBillingTypeEnum
from django.db import transaction
from django.utils import timezone
from accounts.utils.api_services.truecaller import Truecaller
from accounts.services.models import ServiceNumbers, FeatureActivationRequest
from accounts.services.enums import ServiceStatusEnum
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.services.utils.service_package import (
    get_current_service_package,
)
from typing import Union
from datetime import datetime
from accounts.services.enums import FeatureActivationRequestStatusEnums
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.services.events import (
    FeatureDeactivationEvent,
    FeatureActivationEvent,
)

logger = logging.getLogger(__name__)


def get_package_features_by_package_id(package_id: str) -> QuerySet:
    return PackageFeatures.objects.filter(package_id=package_id)


def create_package_feature(data) -> PackageFeatures:
    return PackageFeatures.objects.create(
        package_id=data["package_id"],
        product_feature_id=data["product_feature_id"],
        product_feature_property_id=data.get("product_feature_property_id", 0),
        free_unit=data.get("free_unit", 0),
        additional=data.get("additional", 0),
        rent_per_month=data["rent_per_month"],
        status=data.get("status", constants.PACKAGE_FEATURE_ENABLED),
        last_disabled_date=data["last_disabled_date"],
        is_customer_cancelled=data.get("is_customer_cancelled", False),
    )


def cancel_active_package_feature(
    id: int, did: str, gsn: str, cancelled_date: Union[datetime, str]
) -> None:
    with transaction.atomic():
        package_feature = PackageFeatures.objects.get(id=id)
        package_feature.last_disabled_date = cancelled_date
        package_feature.is_customer_cancelled = True
        package_feature.save()

        PackageFeatureHistory.objects.create(
            package_feature=package_feature,
            event_type=PackageFeatureEventTypeEnums.CANCELLED.value,
        )

    Truecaller().deactivate_feature(
        did,
        gsn,
        deactivation_date=cancelled_date,
        reason="CANCELLED_BY_USER",
    )


def process_feature_deactivation(feature):
    # Each feature deactivation is handled within its own transaction
    with transaction.atomic():
        try:
            # Set status to 0 (deactivated)
            feature.status = PackageFeatureStatusEnum.DISABLED.value
            feature.save()

            # Create entry in `package_feature_history` table
            PackageFeatureHistory.objects.create(
                package_feature=feature,
                event_type=PackageFeatureEventTypeEnums.DEACTIVATED.value,
            )
        except Exception as e:
            logger.title("process_feature_deactivation").critical(e)


def process_paid_feature_activation(service, gsn, feature_data):

    product_feature_id = feature_data["product_feature_id"]
    product_feature_name = feature_data["product_feature_name"]
    ref_id = feature_data["ref_id"]
    amount = feature_data["feature_cost"]
    description = f"{amount} debited for {product_feature_name} activation"
    try:
        with transaction.atomic():
            # Deduct credits
            BillingAccountCredits.deduct_credit_amount(
                service.billing_account, amount, description
            )
            # Create feature activation request entry
            feature_activation_request = (
                FeatureActivationRequest.objects.create(
                    service_id=service.id,
                    product_feature_id=product_feature_id,
                    ref_id=ref_id,
                    is_credit=True,
                )
            )

            # Get the current service package
            current_package = get_current_service_package(service.id)

            # Create a package feature entry
            created_package_feature = create_package_feature(
                {
                    "package_id": current_package.package.id,
                    "product_feature_id": product_feature_id,
                    "rent_per_month": amount,
                    "last_disabled_date": None,
                }
            )
            # Log the event in package feature history
            PackageFeatureHistory.objects.create(
                package_feature=created_package_feature,
                event_type=PackageFeatureEventTypeEnums.ACTIVATED.value,
            )

            feature_activation_request.status = (
                FeatureActivationRequestStatusEnums.SUCCESS.value
            )
            feature_activation_request.save()

        FeatureActivationEvent().company_id(service.gsn).add_feature_with_data(
            feature_activation_request.product_feature.memcache_key,
            {"request_id": feature_activation_request.ref_id},
        ).send()
    except Exception as e:
        logger.title("process_paid_feature_activation").critical(e)
        raise


def get_active_package_features(package_id: str, paid_feature=None) -> QuerySet:
    queryset = PackageFeatures.objects.filter(
        package_id=package_id,
        status=PackageFeatureStatusEnum.ENABLED.value,
    )
    if paid_feature:
        queryset = queryset.filter(product_feature__is_paid=paid_feature)

    queryset = queryset.select_related("product_feature")
    logger.title("Query #get_active_package_features").info(queryset.query)
    return queryset.all()


def process_feature_suspension(feature, gsn):
    # Each feature deactivation is handled within its own transaction
    try:
        with transaction.atomic():
            # Set status to 3 (suspended)
            feature.status = PackageFeatureStatusEnum.SUSPENDED.value
            feature.save()

            # Create entry in `package_feature_history` table
            PackageFeatureHistory.objects.create(
                package_feature=feature,
                event_type=PackageFeatureEventTypeEnums.DEACTIVATED.value,
            )

        FeatureDeactivationEvent().company_id(gsn).add_feature_with_data(
            feature.product_feature.memcache_key,
            {
                "deactivation_date": timezone.now().strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "reason": "NON_PAYMENT",
            },
        ).send()

    except Exception as e:
        logger.title("process_feature_suspension").critical(e)


def process_feature_reactivation(feature, service):
    try:
        with transaction.atomic():
            feature.status = PackageFeatureStatusEnum.ENABLED.value
            feature.save()

            PackageFeatureHistory.objects.create(
                package_feature=feature,
                event_type=PackageFeatureEventTypeEnums.REACTIVATED.value,
            )

        FeatureActivationEvent().company_id(service.gsn).add_feature_with_data(
            feature.product_feature.memcache_key, {}
        ).send()

    except Exception as e:
        logger.title("process_feature_reactivation").critical(e)


def calculate_leg_a_use(
    package_feature: PackageFeatures, use: int, current_leg_a_use: int = 0
) -> int:
    billing_type = package_feature.product_feature.billing_type

    if billing_type == ProductFeatureBillingTypeEnum.NORMAL.value:
        return use
    elif billing_type == ProductFeatureBillingTypeEnum.FIXED.value:
        return package_feature.free_unit + package_feature.additional
    elif billing_type == ProductFeatureBillingTypeEnum.LICENSED.value:
        return max(current_leg_a_use, use)
    elif billing_type == ProductFeatureBillingTypeEnum.EVENT.value:
        return current_leg_a_use + use

    raise NotImplementedError(
        f"billing_type: {billing_type} is not implemented"
    )
