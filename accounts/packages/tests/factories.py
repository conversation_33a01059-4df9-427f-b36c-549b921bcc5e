from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from accounts.discounts.tests.factories import DiscountFactory
from accounts.packages.models import (
    PackageCustoms,
    Packages,
    PackageFeatures,
    PackageFeatureRates,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductFeatureFactory,
    ProductFeaturePropertyFactory,
)
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    PackageCategoryFactory,
)
from accounts.packages import constants
from django.utils import timezone as tz
from accounts.global_packages.enums import PackageForEnum, PackageTypeEnum


class PackageCustomFactory(DjangoModelFactory):

    parent_package = SubFactory(GlobalPackageFactory)
    package_rent = "300.00"

    additional_rent = "100.00"
    additional_cost = "50.00"
    additional_cost_description = Faker("text")
    payment_cycle = Faker("pyint", min_value=0, max_value=999)
    features = Faker("text")
    free = Faker("text")
    property_id = Faker("text")
    deviation = Faker("text")
    created_by = Faker("text")
    accept_status = Faker("random_int", min=0, max=1)
    accepted_by = Faker("text")
    custom_code = Faker("text")
    discount_id = 0
    discount_type = Faker("text")
    discount_data = SubFactory(DiscountFactory)
    business_name = Faker("text")
    is_pack_changed = Faker("random_int", min=0, max=1)
    comment_to_customer = Faker("text")
    pdf_url = Faker("text")

    class Meta:
        model = PackageCustoms


class PackageFactory(DjangoModelFactory):
    product = SubFactory(ProductFactory)
    global_package = SubFactory(GlobalPackageFactory)
    package_custom = None
    name = Faker("word")
    package_type = PackageTypeEnum.MAIN.value
    package_category = SubFactory(PackageCategoryFactory)
    rent_per_month = "200.000"
    renew_cycle = Faker("random_int", min=1, max=10)
    code = Faker("pystr", max_chars=8)
    ocs_flag = Faker("random_element", elements=[0, 1, 2, 3])
    package_for = Faker(
        "random_element",
        elements=[
            PackageForEnum.VIRTUAL_NUMBER.value,
            PackageForEnum.TOLLFREE.value,
            PackageForEnum.MOBILE_TRACKING.value,
        ],
    )
    package_number = Faker("pystr", max_chars=4)
    discount = None

    class Meta:
        model = Packages


class PackageFeatureFactory(DjangoModelFactory):
    package = SubFactory(PackageFactory)
    product_feature = SubFactory(ProductFeatureFactory)
    product_feature_property = SubFactory(ProductFeaturePropertyFactory)
    free_unit = Faker("random_int", min=1, max=100)
    additional = Faker("random_int", min=0, max=50)
    rent_per_month = Faker("pydecimal", left_digits=5, right_digits=3)
    status = 1
    last_disabled_date = None

    class Meta:
        model = PackageFeatures


class PackageFeatureRatesFactory(DjangoModelFactory):
    class Meta:
        model = PackageFeatureRates

    id = Faker("uuid4")
    package_feature = SubFactory(PackageFeatureFactory)
    min = Faker("random_int", min=0, max=100)
    max = Faker("random_int", min=101, max=200)
    rate = Faker("pydecimal", left_digits=7, right_digits=3, positive=True)
