from django.core.exceptions import PermissionDenied
from django.http import Http404

from rest_framework import exceptions as drfexc
from rest_framework import status
from rest_framework.views import exception_handler

from accounts import error_codes
from .error_codes import ERRORS, SERVER_ERROR

errors = dict(ERRORS)


def get_exception_code_message(exc):
    """return exception code and message"""
    if isinstance(exc, drfexc.APIException):
        return (exc.default_code, exc.default_detail)
    return (SERVER_ERROR, errors[SERVER_ERROR])


def custom_exception_handler(exc, context):
    """Custom exception handler, which modify the response by updating its attribute
    the attributes are update as per the custom renderer; as custom renderer expect
    - response.data always a dict
    - dict containing key 'message' (optional)
    - dict containing key 'status' (optional)
    - dict containing key 'code' (optional)
    """
    if isinstance(exc, Http404):
        exc = drfexc.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = drfexc.PermissionDenied()

    response = exception_handler(exc, context)
    if response is not None:
        if isinstance(response.data, list):
            response.data = {"errors": list(response.data)}
        else:
            response.data["errors"] = dict(response.data)  # copy
        code, msg = get_exception_code_message(exc)
        response.data["code"] = code
        response.data["message"] = msg
        response.data["status"] = "error"
    return response


class BaseException(Exception):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_code = error_codes.SERVER_ERROR
    errors = {}

    def __init__(self, message=None, errors=None):
        if message is None:
            message = self.message
        super().__init__(message)

        if errors:
            self.errors = errors

    def message(self):
        return str(self)

    def get_errors(self):
        return self.errors

    def get_error_code(self):
        return self.error_code

    def get_http_status_code(self):
        return self.http_status_code


class ServerError(BaseException):
    message = "Server Error"


class DatabaseInsertionException(BaseException):
    message = "Db Error"


class DataNotFound(BaseException):
    error_code = status.HTTP_404_NOT_FOUND
    message = ""
    http_status_code = status.HTTP_404_NOT_FOUND


class AccountApiException(BaseException):
    message = "Account API Error"
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY


class ExternalAPIException(BaseException):
    message = "Api failed."


class ChatAPIException(BaseException):
    message = "Chat Api failed."


class OTPMaxAttemptException(BaseException):
    http_status_code = status.HTTP_429_TOO_MANY_REQUESTS
    message = "Maximum OTP attempts exceeded."


class OTPException(BaseException):
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY
    message = "OTP Exception."
