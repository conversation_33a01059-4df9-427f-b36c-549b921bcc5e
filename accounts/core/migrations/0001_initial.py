# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BankAccountDetails',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('account_number', models.CharField(max_length=250)),
                ('bank', models.CharField(max_length=200)),
                ('ifsc_code', models.CharField(max_length=200)),
                ('branch', models.CharField(max_length=200)),
                ('address', models.TextField()),
                ('can_send', models.SmallIntegerField(default=1)),
                ('status', models.SmallIntegerField(default=1)),
                ('mode', models.Char<PERSON>ield(max_length=3)),
            ],
            options={
                'db_table': 'bank_account_details',
            },
        ),
        migrations.CreateModel(
            name='BankAccountShares',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('share_by', models.CharField(max_length=10)),
                ('content_json', models.TextField()),
                ('status', models.SmallIntegerField()),
                ('created_by', models.IntegerField()),
                ('created_by_dept', models.IntegerField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'bank_account_shares',
            },
        ),
        migrations.CreateModel(
            name='BankSheetHistories',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('file_name', models.CharField(max_length=250)),
                ('account_no', models.CharField(max_length=250)),
                ('opening_bal', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('closing_bal', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('from_date', models.DateField()),
                ('to_date', models.DateField()),
                ('debit_count', models.IntegerField()),
                ('credit_count', models.IntegerField()),
                ('dr_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('cr_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('created_by', models.CharField(max_length=36)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'bank_sheet_histories',
            },
        ),
        migrations.CreateModel(
            name='CeleryScheduledTask',
            fields=[
                ('id', models.BigAutoField(editable=False, primary_key=True, serialize=False)),
                ('task_id', models.CharField(max_length=255, unique=True)),
                ('task_name', models.CharField(max_length=255)),
                ('args', models.TextField(blank=True, null=True)),
                ('kwargs', models.TextField(blank=True, null=True)),
                ('eta', models.DateTimeField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'django_celery_scheduled_tasks',
            },
        ),
        migrations.CreateModel(
            name='Countries',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('country', models.CharField(max_length=200, null=True, unique=True)),
                ('short_code', models.CharField(help_text='Short Code(for india-IN)', max_length=4, null=True, unique=True)),
                ('code3', models.CharField(max_length=4, null=True)),
                ('c_code', models.CharField(help_text='Country Dialing  Code(+91 for india)', max_length=20, null=True)),
                ('default_time_zone', models.CharField(help_text='utc time zone', max_length=255, null=True)),
                ('timezone_offset', models.CharField(max_length=6, null=True)),
                ('timezone_name', models.CharField(max_length=100, null=True)),
                ('currency_default', models.CharField(max_length=50, null=True)),
                ('currency_code', models.CharField(max_length=250, null=True)),
                ('status', models.CharField(default='1', help_text='Country Dialing  Code(+91 for india)', max_length=5)),
                ('date', models.DateField(auto_now_add=True)),
            ],
            options={
                'db_table': 'countries',
            },
        ),
        migrations.CreateModel(
            name='DocTypes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=250)),
                ('status', models.IntegerField(default=0)),
                ('created', models.DateField(auto_now_add=True)),
                ('modified', models.DateField(auto_now=True)),
            ],
            options={
                'db_table': 'doc_types',
            },
        ),
        migrations.CreateModel(
            name='EmailQueues',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=50, primary_key=True, serialize=False)),
                ('data', models.TextField()),
                ('service_number', models.CharField(max_length=50, null=True)),
                ('billing_account_id', models.CharField(max_length=50, null=True)),
                ('status', models.SmallIntegerField(default=0)),
                ('send_timestamp', models.BigIntegerField(null=True)),
                ('unmature', models.SmallIntegerField(default=0)),
                ('mature', models.SmallIntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'email_queues',
            },
        ),
        migrations.CreateModel(
            name='SmsQueues',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=50, primary_key=True, serialize=False)),
                ('data', models.TextField()),
                ('service_number', models.CharField(max_length=50, null=True)),
                ('billing_account_id', models.CharField(max_length=50, null=True)),
                ('status', models.SmallIntegerField(default=0)),
                ('send_timestamp', models.BigIntegerField(null=True)),
                ('unmature', models.SmallIntegerField(default=0)),
                ('mature', models.SmallIntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'sms_queues',
            },
        ),
        migrations.CreateModel(
            name='StateCodes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('type', models.IntegerField(help_text='1=> state, 2 => Union territories')),
                ('code2', models.CharField(max_length=2)),
                ('code_for_gst', models.CharField(max_length=20, null=True)),
                ('code_for_tin', models.IntegerField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.countries')),
            ],
            options={
                'db_table': 'state_codes',
            },
        ),
        migrations.CreateModel(
            name='TaxVoicetrees',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('country_code', models.CharField(max_length=2)),
                ('tax_name', models.CharField(max_length=200)),
                ('parent', models.IntegerField()),
                ('value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('effective_from', models.DateField()),
                ('effective_till', models.DateField(null=True)),
                ('state', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.statecodes')),
            ],
            options={
                'db_table': 'tax_voicetrees',
            },
        ),
        migrations.AddIndex(
            model_name='smsqueues',
            index=models.Index(fields=['billing_account_id', 'modified'], name='sms_queues_billing_12966b_idx'),
        ),
        migrations.AddIndex(
            model_name='smsqueues',
            index=models.Index(fields=['status'], name='sms_queues_status_47b341_idx'),
        ),
        migrations.AddIndex(
            model_name='emailqueues',
            index=models.Index(fields=['billing_account_id', 'modified'], name='email_queue_billing_8bb474_idx'),
        ),
        migrations.AddIndex(
            model_name='emailqueues',
            index=models.Index(fields=['status'], name='email_queue_status_1c3429_idx'),
        ),
        migrations.AddIndex(
            model_name='doctypes',
            index=models.Index(fields=['status'], name='doc_types_status_9fc0bf_idx'),
        ),
        migrations.AddIndex(
            model_name='countries',
            index=models.Index(fields=['timezone_offset'], name='countries_timezon_a53a5b_idx'),
        ),
        migrations.AddIndex(
            model_name='countries',
            index=models.Index(fields=['status'], name='countries_status_fa6d5d_idx'),
        ),
        migrations.AddField(
            model_name='bankaccountshares',
            name='bank_account',
            field=models.ForeignKey(db_column='bank_account_id', on_delete=django.db.models.deletion.CASCADE, to='core.bankaccountdetails'),
        ),
        migrations.AddIndex(
            model_name='bankaccountdetails',
            index=models.Index(fields=['account_number'], name='bank_accoun_account_3b33de_idx'),
        ),
        migrations.AddIndex(
            model_name='taxvoicetrees',
            index=models.Index(fields=['country_code', 'state_id', 'effective_from', 'effective_till'], name='tax_voicetr_country_203ab5_idx'),
        ),
        migrations.AddIndex(
            model_name='statecodes',
            index=models.Index(fields=['country_id', 'code_for_gst'], name='state_codes_country_05d33c_idx'),
        ),
    ]
