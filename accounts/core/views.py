import logging
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import filters
from accounts.exceptions import BaseException
from rest_framework.exceptions import NotFound
from accounts.core.models import (
    Countries,
    StateCodes,
)
from accounts.core.serializers import (
    CountryStateSerializer,
)

logger = logging.getLogger(__name__)


class CountryStatesListView(generics.ListAPIView):
    serializer_class = CountryStateSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ["id", "name"]
    ordering = ["name"]  # default ordering

    def get_queryset(self):
        country_id = self.kwargs.get("id")
        if not Countries.objects.filter(id=country_id).exists():
            raise NotFound(detail="Invalid Country ID")
        return StateCodes.objects.filter(country_id=country_id)

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())

            data = self.paginate_queryset(queryset)
            serializer = self.get_serializer(data, many=True)
            return self.get_paginated_response(serializer.data)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
