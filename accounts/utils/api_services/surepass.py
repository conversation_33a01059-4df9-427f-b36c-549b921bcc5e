import typing as t
import json
from rest_framework import status
from .base import SurepassBase
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser
from accounts.billing_accounts.exceptions import InvalidGstNumberException

if t.TYPE_CHECKING:
    from accounts.billing_accounts.utils.gst_parser import GSTDetail


class SurepassApi(SurepassBase):
    def gstin_details(self, gst_number: str) -> "GSTDetail":
        url = f"{self.HOST}/api/v1/corporate/gstin-advanced"
        data = json.dumps({"id_number": gst_number})
        response = self.post_json(url, data=data, timeout=self.TIMEOUT)
        response_json = json.loads(response.text)
        if response.status_code == status.HTTP_200_OK:
            return SurepassGSTParser(response_json["data"]).parse()

        message = response_json.get("message", "Invalid GSTIN number.")
        raise InvalidGstNumberException(message)
