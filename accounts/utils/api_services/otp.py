import json
import typing as t

from django.conf import settings

from accounts.exceptions import ExternalAPIException
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.utils.api_services.base import Base
from accounts.utils.api_services.enums import OTPVia
from rest_framework import status
import logging


logger = logging.getLogger(__name__)


class BaseOtpApiService(Base):
    def send_otp(self, *args, **kwargs):
        raise NotImplementedError


class OTPApiService(BaseOtpApiService):
    """Service for sending OTPs via SMS or Call."""

    BASE_URL: str = settings.SMSG_API_CONFIG["HOST"]
    TIMEOUT: int = settings.SMSG_API_CONFIG["TIMEOUT"]

    def get_url(self) -> str:
        # Handle both string and ParseResult objects
        if hasattr(self.BASE_URL, "geturl"):
            base_url = self.BASE_URL.geturl().rstrip("/")
        else:
            base_url = str(self.BASE_URL).rstrip("/")
        return f"{base_url}/index-v3.php"

    def send_otp(
        self,
        phone_number: str,
        country_code: str,
        otp: str,
        via: t.Literal[OTPVia.sms, OTPVia.call] = OTPVia.sms,
    ) -> t.Tuple[int, dict[str, t.Any]]:
        """Send OTP via SMS or Call."""

        is_country_india = country_code == "91"
        template_slug = (
            "myoperator-otp-sms" if via == OTPVia.sms else "myoperator-otp-call"
        )

        if is_country_india:
            app = (
                "myoperator.otp" if via == OTPVia.sms else "myoperator.otp_call"
            )
        else:
            app = (
                "myoperator.otp.international"
                if via == OTPVia.sms
                else "myoperator.otp_call.international"
            )

        payload = {
            "template_slug": template_slug,
            "app": app,
            "county_code": country_code,
            "send_to": phone_number,
            "params": {"otp": otp},
        }

        try:
            response = self.post_json(
                url=self.get_url(),
                data=json.dumps(payload),
                timeout=self.TIMEOUT,
            )
            json_res = response.json()
            logger.info(f"res:{json_res}, status: {response.status_code}")
        except ExternalAPIException as e:
            raise OTPApiException("API call failed.") from e
        except json.JSONDecodeError as e:
            raise OTPApiException("Failed to decode JSON response") from e

        if status.is_success(response.status_code):
            return response.status_code, json_res
        else:
            if response.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
                error_msg = f"{via.value} OTP attempt limit exceeded. Dependency failed with status: {response.status_code}, response: {json_res}"
                logger.error(error_msg)
                raise OTPApiException(error_msg)

            error_msg = f"Failed to send {via.value} OTP. Dependency failed with status: {response.status_code}, response: {json_res}"
            logger.error(error_msg)
            raise OTPApiException(error_msg)
