from accounts.exceptions import BaseException
from rest_framework import status


class NumberNotAvailableException(BaseException):
    message = "Number system number not available"


class NumberDetailsFetchException(BaseException):
    message = ""


class TruecallerApiException(BaseException):
    message = ""


class NumberFeatureActivationException(BaseException):
    message = ""


class PaymentLinkGenerateException(BaseException):
    message = ""


class PayableAmountException(BaseException):
    message = ""


class CampaignException(BaseException):
    message = ""


class CancelSubscriptionException(BaseException):
    message = ""


class BubbleApiException(BaseException):
    message = ""


class OTPApiException(BaseException):
    message = ""
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY
