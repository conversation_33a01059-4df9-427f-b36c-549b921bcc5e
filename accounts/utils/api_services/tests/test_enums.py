import pytest
from enum import Enum

from accounts.utils.api_services.enums import OTPVia


class TestOTPVia:
    """Test cases for OTPVia enum."""

    def test_enum_inheritance(self):
        """Test that OTPVia inherits from <PERSON><PERSON>."""
        assert issubclass(OTPVia, Enum)

    def test_sms_value(self):
        """Test SMS enum value."""
        assert OTPVia.sms.value == "sms"

    def test_call_value(self):
        """Test Call enum value."""
        assert OTPVia.call.value == "call"

    def test_enum_members(self):
        """Test that enum has exactly two members."""
        members = list(OTPVia)
        assert len(members) == 2
        assert OTPVia.sms in members
        assert OTPVia.call in members

    def test_enum_names(self):
        """Test enum member names."""
        assert OTPVia.sms.name == "sms"
        assert OTPVia.call.name == "call"

    def test_enum_comparison(self):
        """Test enum member comparison."""
        assert OTPVia.sms == OTPVia.sms
        assert OTPVia.call == OTPVia.call
        assert OTPVia.sms != OTPVia.call

    def test_enum_string_representation(self):
        """Test string representation of enum members."""
        assert str(OTPVia.sms) == "OTPVia.sms"
        assert str(OTPVia.call) == "OTPVia.call"

    def test_enum_iteration(self):
        """Test iterating over enum members."""
        values = [member.value for member in OTPVia]
        assert "sms" in values
        assert "call" in values
        assert len(values) == 2

    def test_enum_access_by_name(self):
        """Test accessing enum members by name."""
        assert OTPVia["sms"] == OTPVia.sms
        assert OTPVia["call"] == OTPVia.call

    def test_enum_access_by_value(self):
        """Test accessing enum members by value."""
        assert OTPVia("sms") == OTPVia.sms
        assert OTPVia("call") == OTPVia.call

    def test_invalid_enum_access(self):
        """Test accessing invalid enum member raises error."""
        with pytest.raises(ValueError):
            OTPVia("invalid")
        
        with pytest.raises(KeyError):
            OTPVia["invalid"]

    def test_enum_in_conditional(self):
        """Test using enum in conditional statements."""
        via = OTPVia.sms
        
        if via == OTPVia.sms:
            result = "SMS selected"
        elif via == OTPVia.call:
            result = "Call selected"
        else:
            result = "Unknown"
        
        assert result == "SMS selected"

    def test_enum_in_dictionary(self):
        """Test using enum as dictionary keys."""
        otp_methods = {
            OTPVia.sms: "Send via SMS",
            OTPVia.call: "Send via Call"
        }
        
        assert otp_methods[OTPVia.sms] == "Send via SMS"
        assert otp_methods[OTPVia.call] == "Send via Call"

    def test_enum_hashable(self):
        """Test that enum members are hashable."""
        # This should not raise an error
        hash(OTPVia.sms)
        hash(OTPVia.call)
        
        # Test in set
        otp_set = {OTPVia.sms, OTPVia.call}
        assert len(otp_set) == 2
