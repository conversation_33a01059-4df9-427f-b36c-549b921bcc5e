import pytest
from rest_framework import status

from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import BaseException


class TestOTPApiException:
    """Test cases for OTPApiException."""

    def test_inheritance(self):
        """Test that OTPApiException inherits from BaseException."""
        exception = OTPApiException()
        assert isinstance(exception, BaseException)

    def test_http_status_code(self):
        """Test OTPApiException HTTP status code."""
        exception = OTPApiException()
        assert exception.http_status_code == status.HTTP_424_FAILED_DEPENDENCY

    def test_default_message(self):
        """Test OTPApiException default message."""
        exception = OTPApiException()
        assert exception.message == ""

    def test_custom_message(self):
        """Test OTPApiException with custom message."""
        custom_message = "Custom OTP API exception message"
        exception = OTPApiException(custom_message)
        assert str(exception) == custom_message

    def test_string_representation(self):
        """Test string representation of OTPApiException."""
        exception = OTPApiException("Test OTP API exception")
        assert str(exception) == "Test OTP API exception"

    def test_status_code_value(self):
        """Test that status code is 424."""
        exception = OTPApiException()
        assert exception.http_status_code == 424

    def test_empty_default_message(self):
        """Test that default message is empty string."""
        exception = OTPApiException()
        assert exception.message == ""

    def test_exception_with_detailed_message(self):
        """Test exception with detailed error message."""
        detailed_message = (
            "OTP API failed with status 500: Internal Server Error"
        )
        exception = OTPApiException(detailed_message)

        assert str(exception) == detailed_message
        assert exception.http_status_code == status.HTTP_424_FAILED_DEPENDENCY

    def test_exception_raising(self):
        """Test that exception can be raised and caught properly."""
        with pytest.raises(OTPApiException):
            raise OTPApiException("Test OTP API error")

    def test_exception_catching_by_base_class(self):
        """Test that exception can be caught by BaseException."""
        with pytest.raises(BaseException):
            raise OTPApiException("Test OTP API error")

    def test_exception_attributes(self):
        """Test that exception has expected attributes."""
        exception = OTPApiException("Test message")

        # Should have message attribute
        assert hasattr(exception, "message")
        assert hasattr(exception, "http_status_code")

        # Should inherit from BaseException
        assert isinstance(exception, BaseException)

    def test_multiple_exception_instances(self):
        """Test creating multiple exception instances."""
        exception1 = OTPApiException("First error")
        exception2 = OTPApiException("Second error")

        assert str(exception1) == "First error"
        assert str(exception2) == "Second error"
        assert exception1.http_status_code == exception2.http_status_code

    def test_exception_with_no_args(self):
        """Test exception creation with no arguments."""
        exception = OTPApiException()

        # Should not raise any errors
        assert exception.message == ""
        assert exception.http_status_code == status.HTTP_424_FAILED_DEPENDENCY

    def test_exception_repr(self):
        """Test repr representation of the exception."""
        message = "OTP API error occurred"
        exception = OTPApiException(message)

        repr_str = repr(exception)
        assert "OTPApiException" in repr_str
        assert message in repr_str
