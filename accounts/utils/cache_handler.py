from abc import ABC

from django.core.cache import cache


class BaseCache<PERSON><PERSON>ler(ABC):
    def __init__(self, key: str) -> None:
        self._key = key

    def key(self):
        return self._key

    def get(self):
        return cache.get(self._key)

    # @abstractmethod
    def save(self, data, timeout):
        return cache.set(self._key, data, timeout)

    def delete(self):
        cache.delete(self._key)

    def expire(self, time=0):
        cache.expire(self._key, time)

    def ttl(self):
        return cache.ttl(self._key)

    def exists(self):
        return True if cache.get(self._key, None) else False

    def incr(self, timeout=0) -> int:
        try:
            return cache.incr(self._key)
            # cache.touch(self._key, timeout=timeout)
        except ValueError:
            cache.set(self._key, 1, timeout=timeout)
            return 1


class BaseOtpCacheHandler(BaseCacheHandler):
    TTL = 0
    MAX_ATTEMPTS = 10

    def incr(self) -> int:
        return super().incr(self.TTL)

    def attempt_allowed(self) -> bool:
        no_of_attempts = self.get()

        if no_of_attempts is not None and no_of_attempts >= self.MAX_ATTEMPTS:
            return False
        return True

    def set(self, value):
        return super().save(value, self.TTL)
