import pytest
from unittest.mock import patch, MagicMock
import phonenumbers

from accounts.utils.otp import BaseOtpHandler
from accounts.utils.cache_handler import BaseOtpCacheHandler
from accounts.utils.api_services.otp import BaseOtpApiService
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import OTPException


class TestBaseOtpHandler:
    """Test cases for BaseOtpHandler."""

    def test_init(self):
        """Test initialization of BaseOtpHandler."""
        handler = BaseOtpHandler()

        # Check that private attributes are not initialized
        assert not hasattr(handler, "_send_attempt_handler")
        assert not hasattr(handler, "_verification_attempt_handler")
        assert not hasattr(handler, "_otp_cache_handler")
        assert not hasattr(handler, "_api_service")

    def test_generate_otp(self):
        """Test generate_otp static method."""
        otp = BaseOtpHandler.generate_otp()

        assert isinstance(otp, int)
        assert 1000 <= otp <= 9999

    def test_generate_otp_multiple_calls(self):
        """Test generate_otp generates different values."""
        otps = [BaseOtpHandler.generate_otp() for _ in range(10)]

        # All should be 4-digit numbers
        for otp in otps:
            assert 1000 <= otp <= 9999

        # Should have some variation (not all the same)
        assert len(set(otps)) > 1

    def test_send_attempt_handler_property_not_initialized(self):
        """Test send_attempt_handler property raises error when not initialized."""
        handler = BaseOtpHandler()

        with pytest.raises(AttributeError):
            _ = handler.send_attempt_handler

    def test_verification_attempt_handler_property_not_initialized(self):
        """Test verification_attempt_handler property raises error when not initialized."""
        handler = BaseOtpHandler()

        with pytest.raises(AttributeError):
            _ = handler.verification_attempt_handler

    def test_otp_cache_handler_property_not_initialized(self):
        """Test otp_cache_handler property raises error when not initialized."""
        handler = BaseOtpHandler()

        with pytest.raises(AttributeError):
            _ = handler.otp_cache_handler

    def test_api_service_property_not_initialized(self):
        """Test api_service property raises error when not initialized."""
        handler = BaseOtpHandler()

        with pytest.raises(AttributeError):
            _ = handler.api_service

    def test_send_attempt_handler_setter_and_getter(self):
        """Test send_attempt_handler setter and getter."""
        handler = BaseOtpHandler()
        mock_handler = MagicMock(spec=BaseOtpCacheHandler)

        handler.send_attempt_handler = mock_handler

        assert handler.send_attempt_handler == mock_handler

    def test_verification_attempt_handler_setter_and_getter(self):
        """Test verification_attempt_handler setter and getter."""
        handler = BaseOtpHandler()
        mock_handler = MagicMock(spec=BaseOtpCacheHandler)

        handler.verification_attempt_handler = mock_handler

        assert handler.verification_attempt_handler == mock_handler

    def test_otp_cache_handler_setter_and_getter(self):
        """Test otp_cache_handler setter and getter."""
        handler = BaseOtpHandler()
        mock_handler = MagicMock(spec=BaseOtpCacheHandler)

        handler.otp_cache_handler = mock_handler

        assert handler.otp_cache_handler == mock_handler

    def test_api_service_setter_and_getter(self):
        """Test api_service setter and getter."""
        handler = BaseOtpHandler()
        mock_service = MagicMock(spec=BaseOtpApiService)

        handler.api_service = mock_service

        assert handler.api_service == mock_service

    def test_get_country_code_and_phone_number_valid_indian(self):
        """Test get_country_code_and_phone_number with valid Indian number."""
        handler = BaseOtpHandler()

        country_code, phone_number = handler.get_country_code_and_phone_number(
            "+************"
        )

        assert country_code == 91
        assert phone_number == 9876543210

    def test_get_country_code_and_phone_number_valid_us(self):
        """Test get_country_code_and_phone_number with valid US number."""
        handler = BaseOtpHandler()

        country_code, phone_number = handler.get_country_code_and_phone_number(
            "+11234567890"
        )

        assert country_code == 1
        assert phone_number == 1234567890

    def test_get_country_code_and_phone_number_valid_uk(self):
        """Test get_country_code_and_phone_number with valid UK number."""
        handler = BaseOtpHandler()

        country_code, phone_number = handler.get_country_code_and_phone_number(
            "+************"
        )

        assert country_code == 44
        assert phone_number == 7700900123

    def test_get_country_code_and_phone_number_invalid(self):
        """Test get_country_code_and_phone_number with invalid number."""
        handler = BaseOtpHandler()

        with pytest.raises(OTPException, match="Invalid phone number"):
            handler.get_country_code_and_phone_number("invalid_number")

    def test_get_country_code_and_phone_number_empty(self):
        """Test get_country_code_and_phone_number with empty number."""
        handler = BaseOtpHandler()

        with pytest.raises(OTPException, match="Invalid phone number"):
            handler.get_country_code_and_phone_number("")

    def test_get_country_code_and_phone_number_none(self):
        """Test get_country_code_and_phone_number with None."""
        handler = BaseOtpHandler()

        with pytest.raises(OTPException, match="Invalid phone number"):
            handler.get_country_code_and_phone_number(None)

    @patch("phonenumbers.parse")
    def test_get_country_code_and_phone_number_parse_exception(
        self, mock_parse
    ):
        """Test get_country_code_and_phone_number when phonenumbers.parse raises exception."""
        mock_parse.side_effect = phonenumbers.NumberParseException(
            phonenumbers.NumberParseException.INVALID_COUNTRY_CODE,
            "Invalid country code",
        )

        handler = BaseOtpHandler()

        with pytest.raises(OTPException, match="Invalid phone number"):
            handler.get_country_code_and_phone_number("+invalid")

    def test_is_send_max_attempts_reached_allowed(self):
        """Test is_send_max_attempts_reached when attempts are allowed."""
        handler = BaseOtpHandler()
        mock_handler = MagicMock(spec=BaseOtpCacheHandler)
        mock_handler.attempt_allowed.return_value = True

        handler.send_attempt_handler = mock_handler

        result = handler.is_send_max_attempts_reached()

        assert result is False
        mock_handler.attempt_allowed.assert_called_once()

    def test_is_send_max_attempts_reached_not_allowed(self):
        """Test is_send_max_attempts_reached when attempts are not allowed."""
        handler = BaseOtpHandler()
        mock_handler = MagicMock(spec=BaseOtpCacheHandler)
        mock_handler.attempt_allowed.return_value = False

        handler.send_attempt_handler = mock_handler

        result = handler.is_send_max_attempts_reached()

        assert result is True
        mock_handler.attempt_allowed.assert_called_once()

    def test_send_success(self):
        """Test send method success."""
        handler = BaseOtpHandler()
        mock_service = MagicMock(spec=BaseOtpApiService)

        handler.api_service = mock_service

        handler.send(country_code="91", phone_number="9876543210", otp="1234")

        mock_service.send_otp.assert_called_once_with(
            country_code="91", phone_number="9876543210", otp="1234"
        )

    def test_send_api_exception(self):
        """Test send method with OTPApiException."""
        handler = BaseOtpHandler()
        mock_service = MagicMock(spec=BaseOtpApiService)
        mock_service.send_otp.side_effect = OTPApiException("API failed")

        handler.api_service = mock_service

        with pytest.raises(OTPException, match="Failed to send OTP"):
            handler.send(
                country_code="91", phone_number="9876543210", otp="1234"
            )

    def test_send_without_api_service(self):
        """Test send method without api_service initialized."""
        handler = BaseOtpHandler()

        with pytest.raises(AttributeError):
            handler.send(
                country_code="91", phone_number="9876543210", otp="1234"
            )

    def test_property_setters_type_validation(self):
        """Test that property setters accept correct types."""
        handler = BaseOtpHandler()

        # These should not raise errors
        handler.send_attempt_handler = MagicMock(spec=BaseOtpCacheHandler)
        handler.verification_attempt_handler = MagicMock(
            spec=BaseOtpCacheHandler
        )
        handler.otp_cache_handler = MagicMock(spec=BaseOtpCacheHandler)
        handler.api_service = MagicMock(spec=BaseOtpApiService)

    def test_full_workflow_integration(self):
        """Test full workflow integration with all components."""
        handler = BaseOtpHandler()

        # Set up all components
        send_handler = MagicMock(spec=BaseOtpCacheHandler)
        verification_handler = MagicMock(spec=BaseOtpCacheHandler)
        otp_handler = MagicMock(spec=BaseOtpCacheHandler)
        api_service = MagicMock(spec=BaseOtpApiService)

        send_handler.attempt_allowed.return_value = True

        handler.send_attempt_handler = send_handler
        handler.verification_attempt_handler = verification_handler
        handler.otp_cache_handler = otp_handler
        handler.api_service = api_service

        # Test workflow
        assert not handler.is_send_max_attempts_reached()

        country_code, phone_number = handler.get_country_code_and_phone_number(
            "+************"
        )
        assert country_code == 91
        assert phone_number == 9876543210

        handler.send(
            country_code=country_code, phone_number=phone_number, otp="1234"
        )
        api_service.send_otp.assert_called_once_with(
            country_code=91, phone_number=9876543210, otp="1234"
        )
