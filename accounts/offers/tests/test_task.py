import json
import responses

from django.conf import settings
from django.test import TestCase, override_settings
from django.utils import timezone

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.offers.constants import (
    OFFER_PAYMENT_STATUS_PENDING,
    OFFER_PAYMENT_STATUS_REJECTED,
)
from accounts.offers.models import PaymentOffers
from accounts.offers.tests.factories import OfferFactory, PaymentOffersFactory
from accounts.payments.tests.factories import (
    PaymentTracksFactory,
    RechargesFactory,
    TrackingSettlementHistoriesFactory,
)
from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.offers.tasks import process_offer_payment


class TestOfferPaymentTask(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(discount=None)
        self.min_amount = "300.000"
        self.max_amount = "500.000"
        self.offer = OfferFactory.create(
            product=self.product,
            min_amount=self.min_amount,
            max_amount=self.max_amount,
        )

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @responses.activate
    def test_process_offer_payment_with_credit_api_failure(self):
        # test with credit_api_failure
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
            expiry=timezone.now() + timezone.timedelta(days=10),
            status=1,
        )
        recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="abc_123_333",
            amount=400,
            country=self.country,
            status="Y",
        )

        payment_track = PaymentTracksFactory.create(
            payment_id=recharge.payment_id, amount=recharge.amount
        )

        TrackingSettlementHistoriesFactory.create(
            billing_account=recharge.billing_account,
            setl_key=payment_track.txn_setl_key,
            payment=payment_track,
            amount=payment_track.amount,
        )
        payment_offer = PaymentOffersFactory.create(
            offer=offer,
            payment=recharge,
        )

        expected_dict = json.dumps(
            {
                "status": "error",
                "data": "",
                "message": "invalid token",
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "discount/credit_add",
            body=expected_dict,
            status=400,
        )

        process_offer_payment(recharge.payment_id)
        payment_offer = PaymentOffers.objects.get(id=payment_offer.id)
        self.assertEqual(payment_offer.status, OFFER_PAYMENT_STATUS_PENDING)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_process_offer_payment_failed_exception(self):
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
            expiry=timezone.now() - timezone.timedelta(days=10),
            status=1,
        )
        recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="abc_123_333",
            amount=400,
            country=self.country,
            status="Y",
        )

        payment_track = PaymentTracksFactory.create(
            payment_id=recharge.payment_id, amount=recharge.amount
        )

        TrackingSettlementHistoriesFactory.create(
            billing_account=recharge.billing_account,
            setl_key=payment_track.txn_setl_key,
            payment=payment_track,
            amount=payment_track.amount,
        )
        offer_payment = PaymentOffersFactory.create(
            offer=offer,
            payment=recharge,
        )

        # Call the process_offer_payment task
        process_offer_payment(payment_id="abc_123_333")

        offer_payment = PaymentOffers.objects.get(id=offer_payment.id)
        self.assertEqual(offer_payment.status, OFFER_PAYMENT_STATUS_REJECTED)
