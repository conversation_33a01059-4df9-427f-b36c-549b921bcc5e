from django.utils import timezone as tz

from factory.django import DjangoModelFactory

from accounts.discounts.models import Discounts
from accounts.billing_accounts.models import DiscountBuckets
from factory import Faker, SubFactory
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    MonthlyStatementFactory,
)


class DiscountFactory(DjangoModelFactory):
    name = Faker("text", max_nb_chars=200)
    code = Faker("pystr", min_chars=6, max_chars=6)
    term = Faker("random_int", min=1, max=2)
    model = Faker("random_int", min=1, max=2)
    apply_on = Faker("random_int", min=1, max=3)
    period = Faker("random_int", min=1, max=12)
    value = Faker("random_int")
    min_range = Faker("random_int", min=0, max=*********)
    max_range = Faker("random_int", min=0, max=*********)
    product_for = "0"
    valid_till = Faker("future_datetime", tzinfo=tz.get_current_timezone())
    is_public = Faker("random_int", min=0, max=1)

    class Meta:
        model = Discounts


class DiscountBucketsFactory(DjangoModelFactory):

    billing_account = SubFactory(BillingAccountFactory)
    discount = SubFactory(DiscountFactory)
    monthly_statement = SubFactory(MonthlyStatementFactory)

    class Meta:
        model = DiscountBuckets
