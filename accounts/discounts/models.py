from django.db import models

from accounts.utils.common import uuid


class Discounts(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=6, unique=True)
    term = models.IntegerField(default=1, help_text="1 => flat, 2 => percent")
    model = models.IntegerField(
        default=1, help_text="1=> one time, 2 => periodic"
    )
    apply_on = models.IntegerField(help_text="1=> rent, 2=>usages, 3 => all")
    period = models.IntegerField(
        default=1,
        help_text="period is number of months if discount is periodic. if discount is one time then its value is one",
    )
    value = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="this is discount value if flat then value is in amount and if percent then value is in percent",
    )
    min_range = models.IntegerField(
        default=0,
        help_text="can be applied only on amount greated or equal to this value",
    )
    max_range = models.IntegerField(
        help_text="can be applied on amount equal or less than this value"
    )
    product_for = models.CharField(
        max_length=36,
        default="0",
        help_text="0=> valid for all products, else this column will product id which means valid for specific product only",
    )
    valid_till = models.DateTimeField(
        help_text="mentioned discount will be only valid till date"
    )
    is_public = models.SmallIntegerField(
        default=0,
        help_text="0 - do not  show this to anyone, specific to particular package only, 1 - public, can be shared with packages",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "discounts"
