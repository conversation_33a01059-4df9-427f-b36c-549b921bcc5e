import pytest
from rest_framework import status

from accounts.kyc.exceptions import GstOTPMaxAttemptException
from accounts import error_codes


class TestGstOTPMaxAttemptException:
    """Test cases for GstOTPMaxAttemptException."""

    def test_exception_inheritance(self):
        """Test that GstOTPMaxAttemptException inherits from OTPMaxAttemptException."""
        from accounts.exceptions import OTPMaxAttemptException
        
        exception = GstOTPMaxAttemptException("Test message")
        
        assert isinstance(exception, OTPMaxAttemptException)

    def test_error_code(self):
        """Test that the exception has the correct error code."""
        exception = GstOTPMaxAttemptException("Test message")
        
        assert exception.error_code == error_codes.GST_VERIFY_OTP_SEND_MAX_ATTEMPT_EXCEEDED

    def test_http_status_code(self):
        """Test that the exception has the correct HTTP status code."""
        exception = GstOTPMaxAttemptException("Test message")
        
        # Should inherit from OTPMaxAttemptException
        assert exception.http_status_code == status.HTTP_429_TOO_MANY_REQUESTS

    def test_default_message(self):
        """Test that the exception has the correct default message."""
        exception = GstOTPMaxAttemptException()
        
        # Should inherit from OTPMaxAttemptException
        assert exception.message == "Maximum OTP attempts exceeded."

    def test_custom_message(self):
        """Test that the exception can accept a custom message."""
        custom_message = "Custom GST OTP max attempt message"
        exception = GstOTPMaxAttemptException(custom_message)
        
        assert str(exception) == custom_message

    def test_exception_str_representation(self):
        """Test string representation of the exception."""
        message = "GST OTP max attempts exceeded"
        exception = GstOTPMaxAttemptException(message)
        
        assert str(exception) == message

    def test_exception_repr_representation(self):
        """Test repr representation of the exception."""
        message = "GST OTP max attempts exceeded"
        exception = GstOTPMaxAttemptException(message)
        
        assert repr(exception) == f"GstOTPMaxAttemptException('{message}')"
