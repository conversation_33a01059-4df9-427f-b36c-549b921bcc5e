import pytest
from unittest.mock import patch, MagicMock

from accounts.kyc.cache_handler import (
    KycOtpSendAttempt<PERSON>ache<PERSON><PERSON><PERSON>,
    KycOtpVerificationAttempt,
    GstOtpCache<PERSON><PERSON><PERSON>,
    GstCacheHandler,
)
from accounts.billing_accounts.utils.gst_parser import GSTDetail


class TestKycOtpSendAttemptCacheHandler:
    """Test cases for KycOtpSendAttemptCacheHandler."""

    def test_init(self):
        """Test initialization of KycOtpSendAttemptCacheHandler."""
        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        assert handler._key == f"kyc:{gst_number}__otp_attempts"
        assert handler.TTL == 60 * 60 * 24  # 24 hours
        assert handler.MAX_ATTEMPTS == 5

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_first_time(self, mock_cache):
        """Test incrementing attempt count for the first time."""
        mock_cache.incr.side_effect = ValueError("Key does not exist")
        mock_cache.set.return_value = True

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.incr()

        assert result == 1
        mock_cache.incr.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )
        mock_cache.set.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts", 1, timeout=60 * 60 * 24
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_existing_key(self, mock_cache):
        """Test incrementing attempt count for existing key."""
        mock_cache.incr.return_value = 3

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.incr()

        assert result == 3
        mock_cache.incr.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )
        mock_cache.set.assert_not_called()

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_under_limit(self, mock_cache):
        """Test attempt_allowed returns True when under limit."""
        mock_cache.get.return_value = 3

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is True
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_at_limit(self, mock_cache):
        """Test attempt_allowed returns False when at limit."""
        mock_cache.get.return_value = 5

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is False
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_over_limit(self, mock_cache):
        """Test attempt_allowed returns False when over limit."""
        mock_cache.get.return_value = 7

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is False
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_no_attempts(self, mock_cache):
        """Test attempt_allowed returns True when no attempts made."""
        mock_cache.get.return_value = None

        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpSendAttemptCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is True
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )


class TestKycOtpVerificationAttempt:
    """Test cases for KycOtpVerificationAttempt."""

    def test_init(self):
        """Test initialization of KycOtpVerificationAttempt."""
        gst_number = "27AAPFU0939F1ZV"
        handler = KycOtpVerificationAttempt(gst_number)

        assert handler._key == f"kyc:{gst_number}__verification_attempts"
        assert handler.TTL == 60 * 60 * 24  # 24 hours
        assert handler.MAX_ATTEMPTS == 5


class TestGstOtpCacheHandler:
    """Test cases for GstOtpCacheHandler."""

    def test_init(self):
        """Test initialization of GstOtpCacheHandler."""
        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        assert handler._key == f"kyc:{gst_number}__otp"
        assert handler.TTL == 60 * 15  # 15 minutes

    @patch("accounts.utils.cache_handler.cache")
    def test_set_otp_data(self, mock_cache):
        """Test setting OTP data in cache."""
        mock_cache.set.return_value = True

        gst_number = "27AAPFU0939F1ZV"
        phone_number = "+91**********"
        otp = "1234"
        attempt = 2

        handler = GstOtpCacheHandler(gst_number)
        result = handler.set(phone_number, otp, attempt)

        expected_data = {
            "phone_number": phone_number,
            "otp": otp,
            "verification_attempts": attempt,
        }

        assert result is True
        mock_cache.set.assert_called_once_with(
            f"kyc:{gst_number}__otp", expected_data, 60 * 15
        )


class TestGstCacheHandler:
    """Test cases for GstCacheHandler."""

    def test_init(self):
        """Test initialization of GstCacheHandler."""
        key = "test_key"
        handler = GstCacheHandler(key)

        assert handler._key == key
        assert handler.GST_DATA_TTL == 60 * 60 * 24 * 10  # 10 days

    @patch("accounts.utils.cache_handler.cache")
    def test_set_gst_detail_default_timeout(self, mock_cache):
        """Test setting GST detail with default timeout."""
        mock_cache.set.return_value = True

        # Create mock GST detail
        gst_data = {
            "gstin": "27AAPFU0939F1ZV",
            "legal_name": "Test Company",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, 400001",
                }
            },
            "gstin_status": "Active",
        }

        gst_detail = GSTDetail(
            data=gst_data,
            gstin="27AAPFU0939F1ZV",
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Active",
        )

        handler = GstCacheHandler("test_key")
        handler.set(gst_detail)

        mock_cache.set.assert_called_once_with(
            "test_key", gst_data, 60 * 60 * 24 * 10
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_set_gst_detail_custom_timeout(self, mock_cache):
        """Test setting GST detail with custom timeout."""
        mock_cache.set.return_value = True
        custom_timeout = 3600  # 1 hour

        # Create mock GST detail
        gst_data = {
            "gstin": "27AAPFU0939F1ZV",
            "legal_name": "Test Company",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, 400001",
                }
            },
            "gstin_status": "Active",
        }

        gst_detail = GSTDetail(
            data=gst_data,
            gstin="27AAPFU0939F1ZV",
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Active",
        )

        handler = GstCacheHandler("test_key")
        handler.set(gst_detail, timeout=custom_timeout)

        mock_cache.set.assert_called_once_with(
            "test_key", gst_data, custom_timeout
        )
