import logging
import typing as t

from statemachine import Event, State, StateMachine

from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
)
from accounts.kyc.models import KYC

logger = logging.getLogger(__name__)


class KYCStateMachine(StateMachine):
    # states
    verification = State(value=KYCStateEnum.VERIFICATION.value, initial=True)
    gst_info = State(value=KYCStateEnum.GST_INFO.value)
    upload_pan = State(value=KYCStateEnum.UPLOAD_PAN.value)
    digilocker_pan = State(value=KYCStateEnum.DIGILOCKER_PAN.value)
    video_kyc = State(value=KYCStateEnum.VIDEO_KYC.value)
    e_sign = State(value=KYCStateEnum.E_SIGN.value)
    kyc_done = State(value=KYCStateEnum.KYC_DONE.value, final=True)

    # transitions
    # via GST mode
    verification_to_video_kyc = verification.to(
        video_kyc, cond=["is_kyc_mode_gst", "is_verification_completed"]
    )

    # via Aadhaar mode
    verification_to_gst_info = verification.to(
        gst_info, cond=["is_kyc_mode_aadhaar", "is_verification_completed"]
    )
    verification_to_upload_pan = verification.to(
        upload_pan, cond=["is_kyc_mode_aadhaar", "is_verification_completed"]
    )
    verification_to_digilocker_pan = verification.to(
        digilocker_pan,
        cond=["is_kyc_mode_aadhaar", "is_verification_completed"],
    )

    gst_info_to_video_kyc = gst_info.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_gst_info_completed"]
    )
    upload_pan_to_video_kyc = upload_pan.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_upload_pan_completed"]
    )
    digilocker_pan_to_video_kyc = digilocker_pan.to(
        video_kyc, cond=["is_kyc_mode_aadhaar", "is_digilocker_pan_completed"]
    )

    video_kyc_to_e_sign = video_kyc.to(
        e_sign,
        cond=["not is_e_sign_skipped", "is_video_kyc_completed"],
    )
    e_sign_to_kyc_done = e_sign.to(kyc_done, cond=["is_e_sign_completed"])
    video_kyc_to_kyc_done = video_kyc.to(
        kyc_done,
        cond=["is_e_sign_skipped", "is_video_kyc_completed"],
    )

    def __init__(
        self,
        initial_state: t.Optional[str] = None,
        kyc_mode: KYCModeEnum = KYCModeEnum.GST,
    ):
        self.kyc_mode = kyc_mode
        super().__init__()

        if initial_state:
            self.current_state = self.get_state_by_value(initial_state)

    def get_state_by_value(self, state_value: str):
        """Fetches a state by its value.

        Args:
            state_value (str): The value of the state to fetch.

        Raises:
            ValueError: If no state with the given value is found.
        """

        for state in self.states:
            if state.value == state_value:
                return state

        error_msg = (
            "[KYCStateMachine] No KYC state found with value: %s. Failed to instantiate KYCStateMachine."
            % state_value
        )
        logger.critical(error_msg, title="KYC state not found", exc_info=True)
        raise ValueError(error_msg)

    def get_states_mapping(self):
        """Returns a mapping of state values.

        Returns:
            _type_: _description_
        """

        return {
            self.verification.value: {
                KYCModeEnum.GST.value: self.video_kyc.value,
                KYCModeEnum.AADHAAR.value: {
                    self.gst_info.value: self.video_kyc.value,
                    self.upload_pan.value: self.video_kyc.value,
                    self.digilocker_pan.value: self.video_kyc.value,
                },
            },
            self.video_kyc.value: self.e_sign.value,
            self.e_sign.value: self.kyc_done.value,
            self.kyc_done.value: None,
        }

    def on_transition(self, event_data, event: Event):
        """Logs the transition event name with its source and target state values.
        This method is called whenever a transition occurs in the state machine.

        Args:
            event_data (_type_): The data associated with the event.
            event (Event): The event that triggered the transition.
        """

        logger.info(
            "[KYCStateMachine] Running event: %s from state: %s to %s."
            % (
                event.name,
                event_data.transition.source.value,
                event_data.transition.target.value,
            ),
            title="KYC state transition",
        )

    def on_enter_verification(self):
        pass

    def on_exit_verification(self):
        pass

    def is_kyc_mode_gst(self) -> bool:
        return self.kyc_mode == KYCModeEnum.GST

    def is_kyc_mode_aadhaar(self) -> bool:
        return self.kyc_mode == KYCModeEnum.AADHAAR

    def is_verification_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_gst_info_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_upload_pan_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_digilocker_pan_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_video_kyc_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_kyc_done_completed(self) -> bool:
        # Override this method to implement actual logic
        # For example: Determine from the Database
        return True

    def is_e_sign_skipped(self) -> bool:
        # Override this method to implement actual logic
        # Future provision: This is a placeholder for any logic that determines if e_sign is skipped
        return False


class PersistedKYCStateMachine(KYCStateMachine):
    def __init__(self, kyc: KYC):
        self.kyc = kyc
        super().__init__(
            initial_state=self.kyc.current_state,
            kyc_mode=KYCModeEnum(self.kyc.mode),
        )

    def is_verification_completed(self) -> bool:
        """Checks if the verification state is completed.

        Returns:
            bool: True if the verification state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.VERIFICATION)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_gst_info_completed(self) -> bool:
        """Checks if the gst_info state is completed.

        Returns:
            bool: True if the gst_info state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.GST_INFO)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_upload_pan_completed(self) -> bool:
        """Checks if the upload_pan state is completed.

        Returns:
            bool: True if the upload_pan state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.UPLOAD_PAN)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_digilocker_pan_completed(self) -> bool:
        """Checks if the digilocker_pan state is completed.

        Returns:
            bool: True if the digilocker_pan state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.DIGILOCKER_PAN)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_video_kyc_completed(self) -> bool:
        """Checks if the video_kyc state is completed.

        Returns:
            bool: True if the video_kyc state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.VIDEO_KYC)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_e_sign_completed(self) -> bool:
        """Checks if the e_sign state is completed.

        Returns:
            bool: True if the e_sign state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_kyc_done_completed(self) -> bool:
        """Checks if the kyc_done state is completed.

        Returns:
            bool: True if the kyc_done state is completed, False otherwise.
        """

        kyc_state = self.kyc.get_latest_state(state=KYCStateEnum.E_SIGN)

        if not kyc_state:
            return False

        return kyc_state.status == KYCStateStatusEnum.COMPLETED.value

    def is_marked_as_failed(self) -> bool:
        """Checks if the KYC is marked as failed.

        Returns:
            bool: True if the KYC is marked as failed, False otherwise.
        """

        return self.kyc.is_marked_as_failed()

    def mark_as_failed(self, failure_reason: str):
        """Marks the KYC as failed and updates the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC as failed.

        Raises:
            Exception: If an error occurs while marking the KYC as failed.
        """

        self.kyc.mark_as_failed(failure_reason=failure_reason)
