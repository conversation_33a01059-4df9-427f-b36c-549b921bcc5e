from accounts.kyc.cache_handler import (
    GstOtpCacheHand<PERSON>,
    KycOtpSendAttemptCacheHandler,
)
from accounts.kyc.exceptions import GstOTPMaxAttemptException

from accounts.utils.api_services.otp import OTPApiService
from accounts.utils.otp import BaseOtpHandler


class GstOtpHandler(BaseOtpHandler):
    def setup_send_otp(self, gst_number: str):
        self.send_attempt_handler: KycOtpSendAttemptCacheHandler = (
            KycOtpSendAttemptCacheHandler(gst_number)
        )
        self.otp_cache_handler: GstOtpCacheHandler = GstOtpCacheHandler(
            gst_number
        )
        self.api_service: OTPApiService = OTPApiService()

    def _send_otp(self, number: str, otp: str):
        country_code, phone_number = self.get_country_code_and_phone_number(
            number
        )
        self.send(country_code, phone_number, otp)

    def send_otp(self, number: str) -> int:
        if self.is_send_max_attempts_reached():
            raise GstOTPMaxAttemptException(
                f"Send OTP limit exceeded. Maximum allowed: {self.send_attempt_handler.MAX_ATTEMPTS}"
            )
        otp = self.generate_otp()
        attempt = self.send_attempt_handler.incr()
        self.otp_cache_handler.set(number, otp, attempt)

        self._send_otp(number, otp)
        return otp
