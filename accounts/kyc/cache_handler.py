from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.utils.cache_handler import BaseCacheHandler, BaseOtpCacheHandler


class KycOtpSendAttemptCacheHandler(BaseOtpCacheHandler):
    TTL = 60 * 60 * 24  # 24 hours
    MAX_ATTEMPTS = 5

    def __init__(self, gst_number: str):
        super().__init__(f"kyc:{gst_number}__otp_attempts")


class KycOtpVerificationAttempt(BaseOtpCacheHandler):
    TTL = 60 * 60 * 24  # 24 hours
    MAX_ATTEMPTS = 5

    def __init__(self, gst_number: str):
        super().__init__(f"kyc:{gst_number}__verification_attempts")


class GstOtpCacheHandler(BaseOtpCacheHandler):
    TTL = 60 * 15  # 15 minutes

    def __init__(self, gst_number: str):
        super().__init__(f"kyc:{gst_number}__otp")

    def set(self, phone_number: str, otp: str, attempt: int):
        data = {
            "phone_number": phone_number,
            "otp": otp,
            "verification_attempts": attempt,
        }
        return super().save(data, self.TTL)


class GstCacheHandler(BaseCacheHandler):
    GST_DATA_TTL = 60 * 60 * 24 * 10  # 10 days

    def set(self, gstDetail: GSTDetail, timeout=GST_DATA_TTL):
        self.save(gstDetail.data, timeout=timeout)
