import logging
import typing as t
import uuid

from django.db import models, transaction

from accounts.billing_accounts.models import BillingAccounts
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)

logger = logging.getLogger(__name__)


class KYC(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    current_state = models.CharField(
        max_length=30,
        choices=KYCStateEnum.choices(),
        default=KYCStateEnum.VERIFICATION.value,
    )
    mode = models.CharField(
        max_length=20,
        choices=KYCModeEnum.choices(),
    )
    source = models.CharField(
        max_length=20,
        choices=KYCSourceEnum.choices(),
        default=KYCSourceEnum.MYOPERATOR.value,
    )
    status = models.SmallIntegerField(
        choices=KYCStatusEnum.choices(), default=KYCStatusEnum.PENDING.value
    )
    expiry = models.DateTimeField(default=None, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "kycs"

    def get_latest_state(
        self, state: t.Optional[KYCStateEnum] = None
    ) -> t.Optional["KYCState"]:
        """Fetches the latest KYC state. Also, filters by state if provided.

        Args:
            state (KYCStateEnum, optional): The state to filter by. Defaults to None.

        Returns:
            t.Optional[KYCState]: The latest KYC state, or None if no state match.
        """

        kyc_states = self.states.all()

        if state and isinstance(state, KYCStateEnum):
            kyc_states = kyc_states.filter(state=state.value)

        return kyc_states.order_by("-started_at").first()

    def is_marked_as_failed(self) -> bool:
        """Checks if the KYC is marked as failed.

        Returns:
            bool: True if the KYC is marked as failed, False otherwise.
        """

        return self.status == KYCStatusEnum.FAILED.value

    @transaction.atomic
    def mark_as_failed(self, failure_reason: str):
        """Marks the KYC as failed and updates the latest KYC state as failed with the provided reason.

        Args:
            failure_reason (str): The reason for marking the KYC as failed.

        Raises:
            Exception: If an error occurs while marking the KYC as failed.
        """

        logger.info(
            "[KYC] Marking KYC (id: %s) as failed with reason: %s"
            % (self.id, failure_reason),
            title="KYC mark failed",
        )
        self.status = KYCStatusEnum.FAILED.value
        self.save()

        latest_kyc_state = self.states.order_by("-started_at").first()

        if latest_kyc_state:
            logger.info(
                "[KYC] Marking KYC state (id: %s) as failed with reason: %s"
                % (latest_kyc_state.id, failure_reason),
                title="KYC mark failed",
            )
            latest_kyc_state.status = KYCStateStatusEnum.FAILED.value
            latest_kyc_state.failure_reason = failure_reason
            latest_kyc_state.save()


class KYCState(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False
    )
    kyc = models.ForeignKey(
        KYC, on_delete=models.CASCADE, related_name="states"
    )
    state = models.CharField(max_length=30, choices=KYCStateEnum.choices())
    status = models.SmallIntegerField(
        choices=KYCStateStatusEnum.choices(),
        default=KYCStateStatusEnum.PENDING.value,
    )
    data = models.JSONField(default=dict)
    task_id = models.CharField(max_length=36, default=None, null=True)
    failure_reason = models.CharField(max_length=255, default=None, null=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(default=None, null=True)

    objects = models.Manager()

    class Meta:
        db_table = "kyc_states"
