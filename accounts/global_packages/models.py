from django.db import models

from accounts.discounts.models import Discounts
from accounts.products.models import (
    ProductFeatureProperties,
    ProductFeatures,
    Products,
)
from accounts.users.models import Groups
from accounts.utils.common import uuid
from accounts.global_packages import constants
from .enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    StatusEnum as PackageStatusEnum,
    GlobalPackageFeatureStatusEnum,
    PackageFeatureRateSlabStatusEnum,
)


class PackageCategories(models.Model):
    id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    code = models.CharField(max_length=10)
    name = models.CharField(max_length=100)
    proposal_file = models.CharField(max_length=100)
    description = models.TextField()
    weightage = models.IntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "package_categories"
        ordering = ["created"]


class GlobalPackages(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    name = models.CharField(max_length=40)
    package_type = models.CharField(
        max_length=6,
        choices=PackageTypeEnum.choices(),
    )
    rent_per_month = models.DecimalField(max_digits=10, decimal_places=3)
    renew_cycle = models.PositiveIntegerField(
        help_text="shift in package proerpty table"
    )
    is_public = models.BooleanField(default=True)
    code = models.CharField(
        max_length=8,
        help_text="public package->package_for + package_number,custom_plan->random alaphnumeric",
    )
    ocs_flag = models.SmallIntegerField(choices=OcsFlagEnum.choices())
    package_for = models.CharField(
        max_length=15, choices=PackageForEnum.choices()
    )
    package_category = models.ForeignKey(
        PackageCategories, on_delete=models.CASCADE
    )
    package_number = models.CharField(max_length=4)
    discount = models.ForeignKey(
        Discounts, on_delete=models.SET_NULL, null=True
    )
    description = models.TextField(blank=True, null=True, default=None)
    status = models.SmallIntegerField(
        choices=PackageStatusEnum.choices(),
        default=PackageStatusEnum.ACTIVE.value,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "global_packages"
        indexes = [
            models.Index(
                fields=["is_public", "status", "ocs_flag", "product_id"]
            ),
            models.Index(fields=["product_id"]),
            models.Index(fields=["name"]),
            models.Index(fields=["package_type"]),
            models.Index(fields=["rent_per_month"]),
            models.Index(fields=["is_public"]),
            models.Index(fields=["code"]),
            models.Index(fields=["status"]),
            models.Index(fields=["ocs_flag"]),
            models.Index(fields=["package_for"]),
        ]
        ordering = ["created"]


class GlobalPackageFeatures(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    package = models.ForeignKey(GlobalPackages, on_delete=models.CASCADE)
    product_feature = models.ForeignKey(
        ProductFeatures, on_delete=models.CASCADE
    )
    product_feature_property = models.ForeignKey(
        ProductFeatureProperties, on_delete=models.SET_NULL, null=True
    )
    free_unit = models.IntegerField()
    rent_per_month = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.SmallIntegerField(
        choices=GlobalPackageFeatureStatusEnum.choices(),
        help_text="1: enabled, 0:disabled, 2:deleted (option 2 can be set directly from database only, don''t keep this option in frontend)",
        default=GlobalPackageFeatureStatusEnum.ENABLED.value,
    )
    last_disabled_date = models.DateTimeField(null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "global_package_features"
        indexes = [
            models.Index(fields=["package_id"]),
            models.Index(fields=["product_feature_id"]),
            models.Index(fields=["product_feature_property_id"]),
            models.Index(fields=["status"]),
        ]
        ordering = ["created"]

    def get_resource_key(self):
        """
        Returns the resource key for the global package feature.
        """
        resource_key = self.product_feature.resource_key
        if self.product_feature_property_id:
            resource_key = f"{resource_key}{self.product_feature_property.post_fix_resource}"
        return resource_key

    def get_memcache_key(self):
        """
        Returns the memcache key for the global package feature.
        """
        memcache_key = self.product_feature.memcache_key
        if self.product_feature_property_id:
            memcache_key = f"{memcache_key}{self.product_feature_property.post_fix_memcache}"
        return memcache_key


class GlobalPackageFeatureRates(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )  # noqa
    package_feature = models.ForeignKey(
        GlobalPackageFeatures,
        on_delete=models.CASCADE,
        related_name="rate_slabs",
    )
    min = models.IntegerField()
    max = models.IntegerField()
    rate = models.DecimalField(max_digits=10, decimal_places=3)
    status = models.SmallIntegerField(
        default=PackageFeatureRateSlabStatusEnum.ACTIVE.value
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "global_package_feature_rates"
        indexes = [models.Index(fields=["package_feature_id"])]
        ordering = ["created"]


class GlobalPackageGroups(models.Model):
    id = models.AutoField(primary_key=True)
    group = models.ForeignKey(Groups, on_delete=models.CASCADE)
    global_package = models.ForeignKey(GlobalPackages, on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "global_package_groups"
        ordering = ["created"]
