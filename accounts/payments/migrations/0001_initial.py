# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing_accounts', '0001_initial'),
        ('users', '0001_initial'),
        ('core', '0001_initial'),
        ('products', '0001_initial'),
        ('packages', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentSubscriptions',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('gateway', models.CharField(default='stripe', help_text='name of gateway ccavenue or stripe as defined in config', max_length=50)),
                ('customer_ref', models.CharField(help_text='customer reference id received from gateway after registering customer, in case ccavenue it is subscription sub reference', max_length=250, null=True)),
                ('plan_ref', models.CharField(help_text='plan reference number received from gateway after registering plan, in case ccavenue it may not available or directly associated with account plan id', max_length=200, null=True)),
                ('subs_type', models.CharField(help_text='in case of stripe it will not be available but in case ccavenue it will be ONDEMAND', max_length=50, null=True)),
                ('subs_ref', models.CharField(help_text='reference number of subscription received from gateway in case ccavneue it is merchant reference', max_length=250)),
                ('setup_amount', models.DecimalField(decimal_places=3, default=0.0, help_text='in case stripe it will be always zero', max_digits=10)),
                ('currency', models.CharField(default='USD', help_text='currency INR or USD', max_length=5)),
                ('start_date', models.DateTimeField(help_text='subscription start date')),
                ('frequency', models.IntegerField(default=1, help_text='number of frequecy type on which new invoices need to be created.in case of both gateway it will be 1')),
                ('frequency_type', models.CharField(default='months', help_text='in terms of months we need to generate invoices', max_length=50)),
                ('billing_cycle', models.IntegerField(default=12, help_text='plan billing cycle in terms of number of months example 12')),
                ('email', models.CharField(max_length=250)),
                ('phone', models.CharField(max_length=250)),
                ('name', models.CharField(max_length=250)),
                ('addresss', models.TextField(null=True)),
                ('city', models.CharField(max_length=200, null=True)),
                ('country', models.CharField(max_length=200)),
                ('state', models.CharField(max_length=200)),
                ('zip', models.CharField(max_length=50)),
                ('addon', models.TextField(null=True)),
                ('status', models.CharField(default=1, max_length=5)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'payment_subscriptions',
            },
        ),
        migrations.CreateModel(
            name='PaymentTracks',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('account', models.CharField(max_length=250)),
                ('ref_no', models.CharField(max_length=250)),
                ('payment_id', models.CharField(max_length=36, unique=True)),
                ('transaction_date', models.DateTimeField()),
                ('value_date', models.DateTimeField()),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('tr_description', models.TextField(null=True)),
                ('branch', models.CharField(max_length=250, null=True)),
                ('tr_flag', models.CharField(default='cr', help_text='cr => credit, dr => debit', max_length=5)),
                ('tr_status', models.CharField(default='c', help_text='C => Clear, B => Bounce, U => under clear', max_length=5)),
                ('bank_sheet_id', models.CharField(max_length=36, null=True)),
                ('tr_modify_cmnt', models.TextField(null=True)),
                ('claimed', models.IntegerField(default=0, help_text='0 => not claimed, 1 => under process, 2=> claimed')),
                ('client_name', models.CharField(max_length=250, null=True)),
                ('req_amount', models.DecimalField(decimal_places=2, max_digits=10, null=True)),
                ('tds', models.DecimalField(decimal_places=2, max_digits=10, null=True)),
                ('tds_proof', models.CharField(max_length=255, null=True)),
                ('tan', models.CharField(max_length=11, null=True)),
                ('claim_verify', models.IntegerField(default=1, help_text='0=>not verified, 1=> verified')),
                ('claim_verify_by', models.CharField(default='0', max_length=36)),
                ('claim_verify_message', models.CharField(max_length=250, null=True)),
                ('remark', models.TextField()),
                ('claimed_by', models.CharField(max_length=36, null=True)),
                ('claimed_by_dept', models.CharField(default='0', max_length=36)),
                ('aditional_charge', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('txn_setl_key', models.CharField(max_length=10, null=True)),
                ('txn_settle_status', models.IntegerField(default=0, help_text='0=>not settled, 1=>settled manually, 2=>settled auto, 3=>reserved for account use only')),
                ('sub_trxn_status', models.IntegerField(default=0)),
                ('parent_trxn', models.CharField(default='0', max_length=36)),
                ('secure_hash', models.CharField(default='czku4v', max_length=200)),
                ('tr_mode', models.CharField(max_length=36)),
                ('gateway_through', models.CharField(max_length=250, null=True)),
                ('payment_receipt_id', models.CharField(default=None, max_length=36, null=True)),
                ('no_invoice', models.IntegerField(default=0, help_text='if no_invoice is set to 1 the do not generate invoice for such payments')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('state_code', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.statecodes')),
            ],
            options={
                'db_table': 'payment_tracks',
            },
        ),
        migrations.CreateModel(
            name='PaymentTypes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('description', models.CharField(max_length=200)),
                ('date', models.DateField()),
            ],
            options={
                'db_table': 'payment_types',
            },
        ),
        migrations.CreateModel(
            name='Recharges',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('tds_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('selected_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('selected_amount_tax', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('number_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('payment_id', models.CharField(max_length=36, unique=True)),
                ('response_url', models.TextField(null=True)),
                ('response_method', models.CharField(max_length=10, null=True)),
                ('name', models.CharField(max_length=250, null=True)),
                ('email', models.CharField(max_length=250)),
                ('phone', models.CharField(default='0', max_length=200)),
                ('service_number', models.CharField(max_length=200, null=True)),
                ('agent_email', models.CharField(max_length=250, null=True)),
                ('agent_phone', models.CharField(max_length=30, null=True)),
                ('secure_hash', models.CharField(max_length=250)),
                ('recharge_type', models.CharField(default='fix', help_text='fix => fix amount, val => variable amount, pick => 3 option to choose', max_length=50)),
                ('form_visibility', models.SmallIntegerField(default=1)),
                ('tax_calculation', models.SmallIntegerField(default=1, help_text='1=> yes, 0=> no')),
                ('customer_identifier', models.CharField(max_length=70, null=True)),
                ('payment_gateway', models.CharField(max_length=200, null=True)),
                ('gateway_response', models.TextField(null=True)),
                ('general_response', models.TextField(null=True)),
                ('status', models.CharField(default='0', help_text='0 => not done, 1 => under process(requested), 2=> responsed(not processed) Y=> full Paid, P => Partial Paid, N => decline, B => Hold', max_length=5)),
                ('renewal_flag', models.SmallIntegerField(default=0, help_text='0- For All Other 1- For Renewal Automation')),
                ('renewal_services', models.TextField(null=True)),
                ('number_type', models.IntegerField(default=0)),
                ('request_of', models.CharField(max_length=50, null=True)),
                ('payment_method', models.CharField(help_text='selected payment method', max_length=3, null=True)),
                ('page_load_seq', models.CharField(default='0', help_text='page number moving', max_length=3)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('country', models.ForeignKey(default=99, on_delete=django.db.models.deletion.CASCADE, to='core.countries')),
                ('package', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='packages.packages')),
                ('state_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.statecodes')),
            ],
            options={
                'db_table': 'recharges',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionInvoices',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('customer_ref', models.CharField(max_length=200)),
                ('gateway', models.CharField(max_length=50)),
                ('invoice_ref', models.CharField(max_length=200)),
                ('status', models.CharField(max_length=10)),
                ('gateway_resposne', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'subscription_invoices',
            },
        ),
        migrations.CreateModel(
            name='VanDetails',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('van_id', models.CharField(help_text='Virtual account id of razorpay', max_length=50)),
                ('receiver_id', models.CharField(max_length=50)),
                ('receiver_entity', models.CharField(max_length=100)),
                ('receiver_name', models.CharField(max_length=250)),
                ('receiver_account_number', models.CharField(max_length=100)),
                ('receiver_ifsc', models.CharField(max_length=50)),
                ('status', models.IntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'van_details',
            },
        ),
        migrations.CreateModel(
            name='TrackingSettlementHistories',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('setl_key', models.CharField(max_length=200, unique=True)),
                ('amount', models.FloatField(default=0.0)),
                ('created_by', models.CharField(max_length=36, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='payments.paymenttracks', to_field='payment_id')),
            ],
            options={
                'db_table': 'tracking_settlement_histories',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionPaymentAttempt',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('order_id', models.CharField(max_length=200, unique=True)),
                ('payment_id', models.CharField(blank=True, max_length=200, null=True)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('status', models.SmallIntegerField(choices=[(0, 'pending'), (1, 'failed'), (2, 'success')], default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('payment_subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.paymentsubscriptions')),
            ],
            options={
                'db_table': 'subscription_payment_attempts',
            },
        ),
        migrations.CreateModel(
            name='SubscriptionCharges',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('gateway', models.CharField(max_length=250)),
                ('subs_id', models.CharField(max_length=200)),
                ('subscription_activated', models.TextField(null=True)),
                ('charge_status', models.CharField(help_text='charge current status Y=> success, N=> failed', max_length=5)),
                ('charge_response', models.TextField(help_text='charge response', null=True)),
                ('charge_pid', models.CharField(help_text='subscription charge payment id', max_length=200, null=True)),
                ('charge_iid', models.CharField(help_text='charge invoice id', max_length=200, null=True)),
                ('charge_oid', models.CharField(help_text='charge order id', max_length=200, null=True)),
                ('charge_cid', models.CharField(max_length=200, null=True)),
                ('payment_response', models.TextField(help_text='payment event response after charge', null=True)),
                ('order_response', models.TextField(help_text='order response after charge', null=True)),
                ('invoice_response', models.TextField(help_text='invoice response after charge', null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('payment_subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.paymentsubscriptions')),
            ],
            options={
                'db_table': 'subscription_charges',
            },
        ),
        migrations.CreateModel(
            name='RechargeTracks',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('payment_gateway', models.CharField(max_length=200, null=True)),
                ('payment_gateway_ref_id', models.CharField(max_length=50, null=True)),
                ('status', models.IntegerField(default=1, help_text='1 => initiated, 2 => processed')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('recharge', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='payments.recharges')),
            ],
            options={
                'db_table': 'recharge_tracks',
            },
        ),
        migrations.CreateModel(
            name='PaymentUrls',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('agent_id', models.CharField(max_length=50)),
                ('agent_name', models.CharField(max_length=100)),
                ('cust_name', models.CharField(max_length=250, null=True)),
                ('cust_email', models.CharField(max_length=100, null=True)),
                ('cust_mobile', models.CharField(max_length=20, null=True)),
                ('amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('pay_url', models.TextField()),
                ('pay_short_url', models.CharField(max_length=50)),
                ('type', models.IntegerField(default=1, help_text='1 - Anonymous, 2 - BAN type')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.recharges', to_field='payment_id')),
                ('state', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.statecodes')),
            ],
            options={
                'db_table': 'payment_urls',
            },
        ),
        migrations.CreateModel(
            name='PaymentSplits',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('ac_number', models.CharField(help_text='billing account number', max_length=10)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, help_text='amount from payment track', max_digits=10)),
                ('agent_name', models.CharField(max_length=200, null=True)),
                ('payment_flag', models.IntegerField(default=1, help_text='1 => active, 0 => inactive')),
                ('refund_flag', models.IntegerField(default=0, help_text='0 => no refund, 1 => partial refund, 2 => full refund')),
                ('invoice_id', models.CharField(max_length=36)),
                ('invoice_number', models.CharField(max_length=200)),
                ('payment_date', models.DateTimeField()),
                ('parent_id', models.CharField(help_text='parent transaction is in case of splited transactions', max_length=36, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('user_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'payment_splits',
            },
        ),
        migrations.CreateModel(
            name='PaymentReceipts',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('recipt_number', models.CharField(max_length=200)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_mode', models.CharField(max_length=200)),
                ('pdf_url', models.CharField(max_length=250)),
                ('status', models.SmallIntegerField(default=1, help_text='0 => receipt reserved, 1 => receipt generated')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='payments.paymenttracks', to_field='payment_id')),
            ],
            options={
                'db_table': 'payment_receipts',
            },
        ),
        migrations.CreateModel(
            name='PaymentInvoices',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('invoice_number', models.CharField(max_length=250)),
                ('invoice_date', models.DateTimeField()),
                ('company_name', models.CharField(max_length=250)),
                ('company_address', models.TextField()),
                ('amount_details', models.TextField(help_text='amount middle desctiption in terms of json data')),
                ('total_details', models.TextField(help_text='json data including taxes and discount if available')),
                ('payable_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('pdf_url', models.CharField(max_length=250)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.paymenttracks', to_field='payment_id')),
            ],
            options={
                'db_table': 'payment_invoices',
            },
        ),
        migrations.CreateModel(
            name='PaymentFailureReasons',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('name', models.CharField(max_length=250)),
                ('phone', models.CharField(max_length=250)),
                ('email', models.CharField(max_length=250)),
                ('gateway', models.CharField(max_length=200)),
                ('type', models.IntegerField(help_text='1-Normal, 2-Subscription')),
                ('gateway_response', models.TextField()),
                ('reason', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.recharges', to_field='payment_id')),
            ],
            options={
                'db_table': 'payment_failure_reasons',
            },
        ),
        migrations.CreateModel(
            name='PaymentFailureReasonLogs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('search_string', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'payment_failure_reason_logs',
            },
        ),
        migrations.CreateModel(
            name='InvoiceQueues',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('data', models.TextField()),
                ('invoice_date', models.DateTimeField()),
                ('action_response', models.TextField()),
                ('unmature', models.SmallIntegerField(default=0)),
                ('mature', models.SmallIntegerField(default=0)),
                ('status', models.IntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='payments.paymenttracks', to_field='payment_id')),
            ],
            options={
                'db_table': 'invoice_queues',
            },
        ),
        migrations.CreateModel(
            name='InvoiceQueueResponses',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=30, primary_key=True, serialize=False)),
                ('response', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('invoice_queue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='payments.invoicequeues')),
            ],
            options={
                'db_table': 'invoice_queue_responses',
            },
        ),
        migrations.CreateModel(
            name='InvoiceNumbers',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('invo_spec', models.CharField(default='ol', help_text='ol => online, of => offline', max_length=3)),
                ('prefix', models.CharField(max_length=10, null=True)),
                ('current_num', models.IntegerField(default=0)),
                ('fin_year', models.CharField(max_length=10)),
                ('valid_from', models.DateTimeField()),
                ('valid_till', models.DateTimeField()),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'invoice_numbers',
            },
        ),
        migrations.CreateModel(
            name='CcavenueSiRequests',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('si_creation_status', models.CharField(max_length=10)),
                ('si_ref_no', models.CharField(max_length=50)),
                ('si_sub_ref_no', models.CharField(max_length=50)),
                ('email', models.CharField(max_length=250, null=True)),
                ('mobile', models.BigIntegerField(null=True)),
                ('business_name', models.CharField(max_length=250, null=True)),
                ('business_address', models.TextField(null=True)),
                ('business_city', models.CharField(max_length=250, null=True)),
                ('business_state', models.CharField(max_length=250, null=True)),
                ('business_pincode', models.CharField(max_length=30, null=True)),
                ('request_api', models.TextField(null=True)),
                ('response_api', models.TextField(null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'ccavenue_si_requests',
            },
        ),
        migrations.AddIndex(
            model_name='vandetails',
            index=models.Index(fields=['van_id'], name='van_details_van_id_ce09f0_idx'),
        ),
        migrations.AddIndex(
            model_name='vandetails',
            index=models.Index(fields=['billing_account_id', 'van_id'], name='van_details_billing_688d4d_idx'),
        ),
        migrations.AddIndex(
            model_name='trackingsettlementhistories',
            index=models.Index(fields=['billing_account_id'], name='tracking_se_billing_ff0e3c_idx'),
        ),
        migrations.AddIndex(
            model_name='trackingsettlementhistories',
            index=models.Index(fields=['payment_id'], name='tracking_se_payment_94e029_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionpaymentattempt',
            index=models.Index(fields=['payment_id'], name='subscriptio_payment_8f20dc_idx'),
        ),
        migrations.AddIndex(
            model_name='subscriptionpaymentattempt',
            index=models.Index(fields=['payment_subscription'], name='subscriptio_payment_1ea020_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['billing_account_id'], name='recharges_billing_4c7b47_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['package_id'], name='recharges_package_935b97_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['payment_id'], name='recharges_payment_180f29_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['email'], name='recharges_email_7218f0_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['status'], name='recharges_status_2adfb3_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['recharge_type'], name='recharges_recharg_114e2a_idx'),
        ),
        migrations.AddIndex(
            model_name='recharges',
            index=models.Index(fields=['secure_hash'], name='recharges_secure__126824_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenturls',
            index=models.Index(fields=['type'], name='payment_url_type_348eed_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['ref_no'], name='payment_tra_ref_no_6041f9_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['payment_id'], name='payment_tra_payment_a863be_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['transaction_date'], name='payment_tra_transac_65febc_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['claimed'], name='payment_tra_claimed_9e012a_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['claimed_by'], name='payment_tra_claimed_8a4ac0_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['claimed_by_dept'], name='payment_tra_claimed_b5dc0d_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['txn_setl_key'], name='payment_tra_txn_set_cdc71c_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenttracks',
            index=models.Index(fields=['txn_settle_status'], name='payment_tra_txn_set_199ab0_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentreceipts',
            index=models.Index(fields=['payment_id'], name='payment_rec_payment_91bb62_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentinvoices',
            index=models.Index(fields=['billing_account_id'], name='payment_inv_billing_76aeea_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentinvoices',
            index=models.Index(fields=['created'], name='payment_inv_created_79e517_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentinvoices',
            index=models.Index(fields=['payment_id'], name='payment_inv_payment_0d7b05_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentfailurereasons',
            index=models.Index(fields=['payment_id'], name='payment_fai_payment_29a39a_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentfailurereasons',
            index=models.Index(fields=['created'], name='payment_fai_created_a0d860_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicequeues',
            index=models.Index(fields=['billing_account_id'], name='invoice_que_billing_b62bcb_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicequeueresponses',
            index=models.Index(fields=['invoice_queue_id'], name='invoice_que_invoice_305c3d_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicenumbers',
            index=models.Index(fields=['invo_spec', 'valid_from', 'valid_till', 'product_id'], name='invoice_num_invo_sp_a8716c_idx'),
        ),
        migrations.AddIndex(
            model_name='ccavenuesirequests',
            index=models.Index(fields=['billing_account_id'], name='ccavenue_si_billing_9fad76_idx'),
        ),
    ]
