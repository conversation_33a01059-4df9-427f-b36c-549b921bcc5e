from django.utils import timezone

from factory import Faker, SubFactory, Iterator
from factory.django import DjangoModelFactory
from decimal import Decimal
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory, StateCodeFactory
from accounts.packages.tests.factories import PackageFactory
from accounts.payments.models import (
    PaymentTracks,
    Recharges,
    TrackingSettlementHistories,
    PaymentSubscriptions,
)
from accounts.payments.enums import PaymentSubscriptionStatusEnum


class RechargesFactory(DjangoModelFactory):
    billing_account = SubFactory(BillingAccountFactory)
    country = SubFactory(CountryFactory)
    state_code = SubFactory(StateCodeFactory)
    amount = 300
    tds_amount = 100
    selected_amount = 100
    selected_amount_tax = 100
    number_cost = 0
    package = SubFactory(PackageFactory)
    payment_id = Faker("uuid4")
    response_url = Faker("text")
    response_method = Faker("random_element", elements=["GET", "POST"])
    name = Faker("name")
    email = Faker("email")
    phone = Faker("phone_number")
    service_number = Faker("phone_number")
    agent_email = Faker("email")
    agent_phone = Faker("phone_number")
    secure_hash = Faker("sha256")
    recharge_type = Faker("random_element", elements=["fix", "val", "pick"])
    form_visibility = Faker("boolean")
    tax_calculation = Faker("boolean")
    customer_identifier = Faker("uuid4")
    payment_gateway = Faker("word")
    gateway_response = Faker("text")
    general_response = Faker("text")
    status = Faker(
        "random_element", elements=["0", "1", "2", "Y", "P", "N", "B"]
    )
    renewal_flag = Faker("boolean")
    renewal_services = Faker("text")
    number_type = Faker("random_int", min=1, max=9999)
    request_of = Faker("word")
    payment_method = Faker(
        "random_element", elements=["CRD", "CHK", "COD", "BNK"]
    )
    page_load_seq = Faker("random_int", min=1, max=999)

    class Meta:
        model = Recharges


class PaymentTracksFactory(DjangoModelFactory):
    account = Faker("pystr", min_chars=10, max_chars=12)
    ref_no = Faker("pystr", min_chars=10, max_chars=12)
    payment_id = Faker("pystr", min_chars=10, max_chars=12)
    transaction_date = Faker(
        "date_time", tzinfo=timezone.get_current_timezone()
    )
    value_date = Faker("date_time", tzinfo=timezone.get_current_timezone())
    amount = amount = Faker("pyfloat")
    tr_description = Faker("text", max_nb_chars=30)
    branch = None
    tr_flag = "cr"
    tr_status = "c"
    bank_sheet_id = None
    tr_modify_cmnt = Faker("text", max_nb_chars=50)
    claimed = 0
    client_name = None
    req_amount = 0
    tds = 0
    tds_proof = None
    tan = None
    remark = Faker("text", max_nb_chars=10)
    claimed_by = None
    txn_setl_key = Faker("pystr", min_chars=6, max_chars=8)
    txn_settle_status = 0
    sub_trxn_status = 0
    tr_mode = Faker("pystr", min_chars=1, max_chars=2)
    no_invoice = 0

    class Meta:
        model = PaymentTracks


class TrackingSettlementHistoriesFactory(DjangoModelFactory):
    setl_key = Faker("text", max_nb_chars=200)
    billing_account = SubFactory(BillingAccountFactory)
    payment = SubFactory(PaymentTracksFactory)
    amount = Faker("pyfloat")
    created_by = Faker("uuid4")

    class Meta:
        model = TrackingSettlementHistories


class PaymentSubscriptionFactory(DjangoModelFactory):

    gateway = Iterator(["razorpay", "stripe"])
    billing_account = SubFactory(BillingAccountFactory)
    customer_ref = Faker("uuid4")
    plan_ref = Faker("uuid4")
    subs_type = Iterator(["upi", "card"])
    subs_ref = Faker("uuid4")
    setup_amount = Decimal("5.00")
    currency = "INR"
    start_date = Faker("date_time", tzinfo=timezone.get_current_timezone())
    frequency = 1
    frequency_type = "months"
    billing_cycle = 12
    email = Faker("email")
    phone = Faker("phone_number")
    name = Faker("name")
    addresss = Faker("address")
    city = Faker("city")
    country = Faker("country")
    state = Faker("state")
    zip = Faker("postcode")
    addon = Faker("text")
    status = PaymentSubscriptionStatusEnum.ENABLED.value

    class Meta:
        model = PaymentSubscriptions
