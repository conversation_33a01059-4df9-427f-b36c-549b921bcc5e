import typing as t
from datetime import datetime
from decimal import Decimal
from django.db.models import Manager

from accounts.services.enums import (
    ServiceStatusEnum,
    ServiceRentalStatusEnum,
    OtherChargesStatusEnum,
)
from . import constants

from .queryset import (
    ServiceQuerySet,
    ServiceRentalQuerySet,
    ServicePackageQuerySet,
    OtherChargesQuerySet,
    ServiceNumberQuerySet,
    FeatureUsageQuerySet,
)

if t.TYPE_CHECKING:
    from accounts.billing_accounts.models import BillingAccounts
    from accounts.services.models import Services, FeatureUsagesLogs
    from accounts.packages.models import PackageFeatures


class ServiceManager(Manager):
    def get_queryset(self):
        return ServiceQuerySet(self.model, using=self._db)

    def product(self, product_id):
        return self.get_queryset().product(product_id)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_current_usage(self):
        return self.get_queryset().total_current_usage()


class LiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                live_status__in=[
                    constants.SERVICE_LIVE_STATUS_PREPAID,
                    constants.SERVICE_LIVE_STATUS_POSTPAID,
                ]
            )
        )


class ActiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super().get_queryset().filter(status=ServiceStatusEnum.ACTIVE.value)
        )


class SuspendedServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceStatusEnum.SUSPENDED.value)
        )


class InactiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceStatusEnum.INACTIVE.value)
        )


class ServiceRentalManager(Manager):
    def get_queryset(self):
        return ServiceRentalQuerySet(self.model, using=self._db)

    def service(self, service_id):
        return self.get_queryset().service(service_id)

    def active(self):
        return self.get_queryset().active()

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_pending_rental(self):
        return self.get_queryset().total_pending_rental()


class ActiveServiceRentalManager(ServiceRentalManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceRentalStatusEnum.ACTIVE.value)
        )


class ServicePackageManager(Manager):
    def get_queryset(self):
        return ServicePackageQuerySet(self.model, using=self._db)

    def service(self, service_id):
        return self.get_queryset().service(service_id)

    def active(self, time):
        return self.get_queryset().active(time)

    def active_packages(self, time):
        return self.get_queryset().active_packages(time)


class OtherChargesManager(Manager):
    def get_queryset(self):
        return OtherChargesQuerySet(self.model, using=self._db)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_other_charges(self) -> Decimal:
        return self.get_queryset().total_other_charges()


class UnpaidOtherChargesManager(OtherChargesManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=OtherChargesStatusEnum.UNPAID.value)
        )


class ServiceNumberManager(Manager):
    def get_queryset(self):
        return ServiceNumberQuerySet(self.model, using=self._db)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_unpaid_number_cost(self) -> Decimal:
        return self.get_queryset().total_unpaid_number_cost()


class FeatureUsageManager(Manager):
    def get_queryset(self):
        return FeatureUsageQuerySet(self.model, using=self._db)

    def service(self, service: "Services"):
        return self.get_queryset().service(service)

    def find_max_leg_a_usage_instance(
        self,
        service: "Services",
        package_feature: "PackageFeatures",
        start_date: datetime,
    ) -> "FeatureUsagesLogs":
        return self.get_queryset().find_max_leg_a_usage_instance(
            service, package_feature, start_date
        )
