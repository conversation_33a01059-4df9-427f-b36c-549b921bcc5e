from django.db import transaction
from rest_framework import serializers

from accounts.services.enums import ServiceLiveStatusEnum, ServiceStatusEnum
from accounts.services.models import (
    ServicePackages,
    ResourceCharges,
    Services,
)
from accounts.billing_accounts.models import (
    BillingAccountCredits,
    BillingAccounts,
)
from accounts.services.exceptions import (
    ResourceChargeAlreadyExistsException,
    InsufficientCreditsException,
)
from accounts.services.utils.pending_usage import calculate_usage
from accounts.products.models import ProductFeatures, ProductDefaultRateSlabs
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.products.enums import ProductStatusEnum
from django.db.models import Sum
from accounts.services.utils.service_package import get_current_service_package
from rest_flex_fields import FlexFieldsModelSerializer
from accounts.billing_accounts.serializers import BillingSerializer


class PaymentLinkSerializer(serializers.Serializer):
    package_id = serializers.CharField()
    service_number = serializers.CharField()
    custom_amount = serializers.DecimalField(
        default=None,
        max_digits=10,
        decimal_places=3,
        allow_null=True,
    )
    redirect_url = serializers.URLField(default=None)
    payment_method = serializers.IntegerField(default=None)
    name = serializers.CharField(default=None)
    email = serializers.EmailField(default=None)
    phone = serializers.CharField(default=None)

    def validate_custom_amount(self, value):
        if value is not None and value <= 0:
            raise serializers.ValidationError(
                "Custom amount must be greater than 0."
            )
        return value


class ServicePackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServicePackages
        fields = (
            "id",
            "service_id",
            "package_id",
            "start_time",
            "end_time",
        )


class ResourceChargeSerializer(serializers.ModelSerializer):
    resource_id = serializers.CharField()
    resource = serializers.CharField()
    deduct_amount = serializers.DecimalField(
        source="estimated_amount", max_digits=10, decimal_places=2
    )

    class Meta:
        model = ResourceCharges
        fields = ("resource_id", "resource", "deduct_amount")

    def validate_resource_id(self, value):
        if ResourceCharges.objects.filter(resource_id=value).exists():
            raise ResourceChargeAlreadyExistsException()
        return value

    def validate_deduct_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError(
                "deduct_amount must be greater than 0"
            )

        service = self.context.get("service")
        estimated_amount = value
        usage = calculate_usage(service.billing_account)
        # raise error if deduct amount is greater than available amount
        if usage["available"] < estimated_amount:
            raise InsufficientCreditsException()
        return value

    def create(self, validated_data):
        service = self.context["service"]
        validated_data["service"] = service
        amount = self.validated_data.get("estimated_amount")
        resource = self.validated_data.get("resource")
        resource_id = self.validated_data.get("resource_id")
        description = f"{amount} debited for {resource} {resource_id}"

        with transaction.atomic():
            BillingAccountCredits.deduct_credit_amount(
                service.billing_account, amount, description
            )
            return super().create(validated_data)


class PendingUsagesResponseSerializer(serializers.Serializer):
    pending_rental = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    pending_usages = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    current_usages = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    other_charges = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    service_number_cost = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    discount = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    advance = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    available = serializers.DecimalField(
        max_digits=10, decimal_places=3, coerce_to_string=False
    )
    currency = serializers.CharField(read_only=True)

    class Meta:
        fields = "__all__"


class FeatureSerializer(serializers.Serializer):
    product_feature_id = serializers.CharField(max_length=50)
    ref_id = serializers.CharField(max_length=20)

    def validate_product_feature_id(self, value):
        # Retrieve gsn from context
        gsn = self.context.get("gsn")

        # Check if product_feature_id exists
        if not ProductFeatures.objects.filter(
            id=value, status=ProductStatusEnum.ACTIVE.value
        ).exists():
            raise serializers.ValidationError(
                f"Invalid product feature ID '{value}'"
            )

        services = Services.objects.filter(gsn=gsn).first()
        if not services:
            raise serializers.ValidationError("Invalid GSN")

        service_package = get_current_service_package(services.id)
        if not service_package:
            raise serializers.ValidationError("Service package not found")

        # Validate if product_feature_id with gsn exists (product feature already active)
        if Services.objects.filter(
            gsn=gsn,
            servicepackages=service_package,
            servicepackages__package__packagefeatures__product_feature_id=value,
            servicepackages__package__packagefeatures__status=PackageFeatureStatusEnum.ENABLED.value,
        ).exists():
            raise serializers.ValidationError(
                f"Product feature is already active for ID '{value}'"
            )
        return value


class FeatureActivationRequestSerializer(serializers.Serializer):
    features = FeatureSerializer(many=True)  # List of features
    payment_id = serializers.CharField(max_length=50)
    service_id = serializers.CharField(max_length=50, read_only=True)

    def validate(self, data):
        gsn = self.context.get("gsn")
        data["service_id"] = Services.objects.get(gsn=gsn).id
        return data


class FeatureActivateSerializer(serializers.Serializer):
    product_feature_id = serializers.CharField(max_length=50)
    product_feature_name = serializers.CharField(max_length=50, read_only=True)
    ref_id = serializers.CharField(max_length=255)
    feature_cost = serializers.SerializerMethodField(read_only=True)

    def validate(self, data):
        gsn = self.context.get("gsn")
        product_feature_id = data["product_feature_id"]

        # Validate that the product feature exists and is active
        product_feature = ProductFeatures.objects.filter(
            id=product_feature_id, status=ProductStatusEnum.ACTIVE.value
        )
        if not product_feature.exists():
            raise serializers.ValidationError(
                f"Invalid product feature ID '{product_feature_id}'"
            )
        data["product_feature_name"] = product_feature.first().name

        services = Services.objects.filter(gsn=gsn).first()
        if not services:
            raise serializers.ValidationError("Invalid GSN")

        service_package = get_current_service_package(services.id)
        if not service_package:
            raise serializers.ValidationError("Service package not found")

        # Check if the product feature is already active for the given GSN
        is_feature_active = Services.objects.filter(
            gsn=gsn,
            servicepackages=service_package,
            servicepackages__package__packagefeatures__product_feature_id=product_feature_id,
            servicepackages__package__packagefeatures__status=PackageFeatureStatusEnum.ENABLED.value,
        ).exists()
        if is_feature_active:
            raise serializers.ValidationError(
                f"Product feature is already active for ID '{product_feature_id}'"
            )

        """
        Fetch the cost of the product feature from the ProductDefaultRateSlabs model.
        """
        feature = ProductDefaultRateSlabs.objects.filter(
            product_feature_id=product_feature_id
        ).first()
        data["feature_cost"] = feature.rate if feature else 0
        return data


class FeatureActivateListSerializer(serializers.ListSerializer):
    """
    Serializer for validating a list of feature activation requests.
    """

    child = FeatureActivateSerializer()

    def validate(self, data):
        # Check if credit is available for the GSN
        service = self.context.get("service")
        usage = calculate_usage(service.billing_account)
        available_credits = usage.get("available", 0)
        # Extract product feature IDs from the request
        feature_ids = [item["product_feature_id"] for item in data]

        #  Fetch corresponding ProductFeature entries to calculate total cost
        features = ProductDefaultRateSlabs.objects.filter(
            product_feature__in=feature_ids, product_feature__is_paid=True
        )

        # Calculate the total cost
        total_cost = (
            features.aggregate(total_cost=Sum("rate"))["total_cost"] or 0
        )
        # raise error if add-on amount is greater than available amount
        if available_credits < total_cost:
            raise InsufficientCreditsException()

        return data


class BillingAccountSerializer(BillingSerializer):
    auto_bill_email = (
        auto_bill_sms
    ) = cr_limit_email = cr_limit_sms = discount_id = account_manager_id = None
    total_pending = serializers.FloatField(
        required=False,
    )

    class Meta:
        model = BillingAccounts
        exclude = (
            "org_type",
            "parent",
            "discount",
            "auto_bill_email",
            "auto_bill_sms",
            "cr_limit_email",
            "cr_limit_sms",
        )


class ServiceExpandableSerializer(FlexFieldsModelSerializer):
    status = serializers.SerializerMethodField(read_only=True)
    live_status = serializers.SerializerMethodField(read_only=True)

    def get_status(self, obj: Services):
        try:
            return ServiceStatusEnum.get_name(obj.status).lower()
        except ValueError:
            return ""

    def get_live_status(self, obj: Services):
        try:
            return ServiceLiveStatusEnum.get_name(obj.live_status).lower()
        except ValueError:
            return ""

    class Meta:
        model = Services
        expandable_fields = {"billing_account": BillingAccountSerializer}
        fields = (
            "id",
            "product_id",
            "billing_account_id",
            "gsn",
            "user_profile_id",
            "country_id",
            "timezone",
            "rent_per_month",
            "renew_cycle",
            "activation_date",
            "expiry_date",
            "last_renewal",
            "live_status",
            "status",
            "churn_date",
            "current_usages",
            "created",
            "modified",
        )


class ServiceBillingDetailSerializer(serializers.Serializer):
    ac_number = serializers.CharField(read_only=True)
    billing_account_id = serializers.CharField(read_only=True)
    is_corporate = serializers.BooleanField(read_only=True)
    child_billing_account_id = serializers.CharField(read_only=True)
