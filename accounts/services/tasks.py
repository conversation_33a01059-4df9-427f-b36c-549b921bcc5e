import logging
import celery

from django.conf import settings
from accounts.services.utils.service_activation import service_activation
from accounts.services.models import ResourceCharges
from datetime import timedelta
from django.utils import timezone
from accounts.services.utils.resource_refund import process_refund_for_resource
from accounts.exceptions import Chat<PERSON>IException
from accounts.services.exceptions import (
    FeatureProcessingFailedException,
    InvalidServiceException,
    InvalidFeaturePaymentException,
)
from accounts.services.utils.feature_processing import (
    process_pending_feature_payment,
)
from accounts.utils.api_services.exceptions import TruecallerApiException
from accounts.services.utils.service import reactivate_feature

logger = logging.getLogger(__name__)


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def service_activation_task(
    task: celery.Task, ban_id: str, payment_id: str
) -> None:
    task.update_state(state=celery.states.STARTED)
    logger.title("input #service_activation_task").info(locals())
    try:
        service_activation(ban_id, payment_id)
        logger.info(
            f"Service activation processed for payment_id: {payment_id}"
        )
        task.update_state(state=celery.states.SUCCESS)
    except Exception as e:
        logger.title("Service Activation Task Failed").critical(
            e, exc_info=True
        )
        task.update_state(state=celery.states.FAILURE)
        raise


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def refund_pending_resource_charges(task: celery.Task) -> None:
    """
    Task that runs every hour to process records
    that are in a 'refund pending' state for more than 24 hours.

    This method filters the records, processes them accordingly, and
    updates their status to reflect the refund processing.
    """
    task.update_state(state=celery.states.STARTED)

    # Calculate the cutoff times
    start_time = timezone.now() - timedelta(
        days=settings.RESOURCE_REFUND_CONFIG["MAX_REFUND_DAYS"]
    )
    end_time = timezone.now() - timedelta(
        seconds=settings.RESOURCE_REFUND_CONFIG["REFUND_AFTER"]
    )

    # Get records in refund pending state,
    # created more than 24 hours ago but within the last 7 days,
    # limited to 100 records

    query_set = ResourceCharges.objects.filter(
        is_refunded=False,
        created__gte=start_time,
        created__lt=end_time,
    ).order_by("created")
    pending_refund_count = query_set.count()

    if pending_refund_count == 0:
        logger.info("No pending refunds to process.")
        return

    logger.info(f"Found {pending_refund_count} pending refunds to process.")
    try:
        # fetch the first 100 records to refund
        for resource in query_set[:100]:
            try:
                process_refund_for_resource(resource)
            except ChatAPIException as e:
                logger.title("resource charges refund failed").critical(
                    e, exc_info=True
                )
    except Exception as e:
        logger.title("resource charges refund failed").critical(
            f"Error processing pending refunds: {e}", exc_info=True
        )
        raise


@celery.shared_task(
    bind=True,
    max_retries=settings.CELERY_TASK_MAX_RETRIES,
    queue=settings.CELERY_TASK_DEFAULT_QUEUE,
)
def process_feature_payment(task: celery.Task, payment_id: str) -> None:
    task.update_state(state=celery.states.STARTED)
    try:
        try:
            process_pending_feature_payment(payment_id)
        except InvalidServiceException as e:
            logger.title("Feature payment Task Error").error(e, exc_info=True)
            task.update_state(state=celery.states.RETRY)
            raise task.retry(countdown=10)
        logger.info(f"Feature payment processed for payment_id: {payment_id}")
        task.update_state(state=celery.states.SUCCESS)
    except celery.exceptions.Retry as e:
        logger.title("Feature payment Task Retry").info(str(e))
    except celery.exceptions.MaxRetriesExceededError as e:
        task.update_state(state=celery.states.FAILURE)
        logger.title("Feature payment Task Failed").critical(e, exc_info=True)
    except (
        FeatureProcessingFailedException,
        InvalidFeaturePaymentException,
        TruecallerApiException,
    ) as e:
        logger.title("Feature payment Task Failed").error(e)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Feature payment Task Failed").critical(e, exc_info=True)
        raise


@celery.shared_task(
    bind=True,
    max_retries=settings.CELERY_TASK_MAX_RETRIES,
    queue=settings.CELERY_TASK_DEFAULT_QUEUE,
)
def reactivate_service_feature(task: celery.Task, gsn: str) -> None:
    task.update_state(state=celery.states.STARTED)
    try:
        try:
            reactivate_feature(gsn)
        except InvalidServiceException as e:
            logger.title("Reactivate Service Paid Feature Task Error").error(
                e, exc_info=True
            )
            task.update_state(state=celery.states.RETRY)
            raise task.retry(countdown=10)
        logger.info(f"Reactivate Service Paid Feature gsn: {gsn}")
        task.update_state(state=celery.states.SUCCESS)
    except celery.exceptions.Retry as e:
        logger.title("Reactivate Service Paid Feature Task Retry").info(str(e))
    except celery.exceptions.MaxRetriesExceededError as e:
        task.update_state(state=celery.states.FAILURE)
        logger.title("Reactivate Service Paid Feature Task Failed").critical(
            e, exc_info=True
        )
        raise
    except Exception as e:
        logger.title("Reactivate Service Paid Feature Task Failed").critical(
            e, exc_info=True
        )
        raise
