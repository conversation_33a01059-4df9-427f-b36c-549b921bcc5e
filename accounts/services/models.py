from django.db import models
from django.utils import timezone

from accounts.billing_accounts.models import BillingAccounts
from accounts.core.models import Countries
from accounts.packages.models import PackageFeatures, Packages
from accounts.products.models import Products
from accounts.users.models import UserProfiles
from accounts.utils.common import uuid
from accounts.services.enums import (
    ResourceTypeEnums,
    FeatureActivationRequestStatusEnums,
    ServiceStatusEnum,
    ServiceNumberStatusEnum,
    ServiceNumberArchiveEnum,
    ServiceRentalStatusEnum,
)
from . import constants, managers
from accounts.products.models import ProductFeatures
from accounts.payments.models import Recharges


class Services(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    product = models.ForeignKey(Products, on_delete=models.DO_NOTHING)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE, related_name="service"
    )
    gsn = models.CharField(max_length=200, unique=True)
    user_profile = models.ForeignKey(
        UserProfiles, on_delete=models.DO_NOTHING, null=True, default=None
    )
    country = models.ForeignKey(Countries, on_delete=models.DO_NOTHING)
    timezone = models.CharField(max_length=200, default="00:00")
    rent_per_month = models.FloatField(default=0.00)
    renew_cycle = models.IntegerField()
    activation_date = models.DateTimeField(null=True)
    expiry_date = models.DateTimeField()
    last_renewal = models.DateTimeField(null=True)
    live_status = models.IntegerField(
        help_text="1 => post paid, 2 => prepaid, 3 => demo, 6 => mobile sync",
        default=constants.SERVICE_LIVE_STATUS_DEMO,
    )
    status = models.IntegerField(
        help_text="0 => inactive , 1 => active, 2=>suspended, 3 => temporary suspend due to activation date change",
        default=constants.SERVICE_STATUS_ACTIVE,
    )
    churn_date = models.DateTimeField(null=True)
    comment = models.TextField(null=True)
    current_usages = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects: "managers.ServiceManager" = managers.ServiceManager()
    live = managers.LiveServiceManager()
    active = managers.ActiveServiceManager()
    suspended = managers.SuspendedServiceManager()
    inactive = managers.InactiveServiceManager()

    def activate(self):
        self.status = constants.SERVICE_STATUS_ACTIVE
        self.save()

    def suspend(self):
        self.status = ServiceStatusEnum.SUSPENDED.value
        self.save()

    class Meta:
        db_table = "services"
        indexes = [
            models.Index(fields=["created"]),
            models.Index(fields=["user_profile_id"]),
            models.Index(fields=["status"]),
            models.Index(fields=["live_status"]),
            models.Index(fields=["expiry_date"]),
            models.Index(fields=["activation_date"]),
            models.Index(fields=["modified"]),
        ]
        constraints = [
            models.UniqueConstraint(fields=["gsn"], name="unique_gsn"),
            models.UniqueConstraint(
                fields=["billing_account_id"], name="unique_billing_account_id"
            ),
        ]

    def is_demo(self):
        return (
            True
            if self.live_status == constants.SERVICE_LIVE_STATUS_DEMO
            else False
        )

    def deactivate(self):
        self.churn_date = timezone.now()
        self.status = ServiceStatusEnum.INACTIVE.value
        self.save()

    def set_expiry_date(self, expiry_date):
        self.expiry_date = expiry_date
        self.save()

    def is_active(self):
        return self.status == ServiceStatusEnum.ACTIVE.value


class ServiceRentals(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    rental_amount = models.FloatField(default=0.00)
    pending_rental = models.FloatField(default=0.00)
    trans_type = models.CharField(max_length=10)
    status = models.SmallIntegerField(
        default=ServiceRentalStatusEnum.ACTIVE.value
    )
    date = models.DateField()

    objects: "managers.ServiceRentalManager" = managers.ServiceRentalManager()
    entries = managers.ServiceRentalManager()
    active = managers.ActiveServiceRentalManager()

    class Meta:
        db_table = "service_rentals"
        indexes = [
            models.Index(fields=["service_id"]),
            models.Index(fields=["service_id", "status"]),
        ]


class ServicePackages(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    package = models.ForeignKey(Packages, on_delete=models.DO_NOTHING)
    package_type = models.CharField(
        max_length=5,
        default="main",
        help_text="main => package, addon => addon package",
    )
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects: "managers.ServicePackageManager" = managers.ServicePackageManager()

    class Meta:
        db_table = "service_packages"
        indexes = [
            models.Index(fields=["service_id", "start_time", "end_time"]),
            models.Index(fields=["package_id"]),
            models.Index(fields=["end_time"]),
        ]

    def expire(self):
        self.end_time = timezone.now()
        self.save()


class ServiceNumbers(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    service_number = models.CharField(max_length=50)
    number_type = models.CharField(
        max_length=20, help_text="1:landline, 2:mobile, 3:tollfree"
    )
    number_cost = models.IntegerField()
    is_paid = models.SmallIntegerField()
    is_live = models.SmallIntegerField(default=0)
    is_archived = models.SmallIntegerField(
        default=ServiceNumberArchiveEnum.FALSE.value
    )
    archive_date = models.DateTimeField(null=True)
    swapped_date = models.DateTimeField(null=True)
    status = models.IntegerField(default=ServiceNumberStatusEnum.ACTIVE.value)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()
    entries: "managers.ServiceNumberManager" = managers.ServiceNumberManager()

    class Meta:
        db_table = "service_numbers"
        indexes = [
            models.Index(fields=["service_id"]),
            models.Index(fields=["service_number"]),
            models.Index(fields=["number_cost"]),
            models.Index(fields=["is_paid"]),
            models.Index(fields=["is_live"]),
            models.Index(fields=["is_archived"]),
            models.Index(fields=["archive_date"]),
            models.Index(fields=["status"]),
            models.Index(fields=["created"]),
        ]

    def set_archive_date(self, archive_date: timezone) -> None:
        self.is_archived = constants.SERVICE_NUMBER_ARCHIVED_NO
        self.archive_date = archive_date
        self.status = constants.SERVICE_NUMBER_STATUS_INACTIVE
        self.save()

    def activate(self):
        self.status = constants.SERVICE_NUMBER_STATUS_ACTIVE
        self.archive_date = None
        self.save()


class OtherCharges(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    charge = models.DecimalField(max_digits=10, decimal_places=3)
    description = models.TextField(null=True)
    status = models.SmallIntegerField()
    date = models.DateField()
    pay_date = models.DateTimeField(null=True)

    objects: "managers.OtherChargesManager" = managers.OtherChargesManager()
    unpaid = managers.UnpaidOtherChargesManager()

    class Meta:
        db_table = "other_charges"
        indexes = [models.Index(fields=["service_id", "status", "date"])]


class EventActions(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    team_type = models.CharField(max_length=50, default="renewal")
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    objects = models.Manager()

    class Meta:
        db_table = "event_actions"


class EventReasons(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=250)
    event_action = models.ForeignKey(
        EventActions,
        on_delete=models.DO_NOTHING,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    objects = models.Manager()

    class Meta:
        db_table = "event_reasons"


class RenewRetains(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    product = models.ForeignKey(Products, on_delete=models.DO_NOTHING)
    product_name = models.CharField(max_length=200)
    product_currency = models.CharField(max_length=20, default="INR")
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    ban = models.CharField(max_length=30)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    service_number = models.CharField(max_length=200)
    business_name = models.CharField(max_length=250)
    account_manager = models.CharField(max_length=200, null=True, blank=True)
    account_manager_id = models.CharField(max_length=36, null=True, blank=True)
    last_renewal = models.DateTimeField()
    expiry_date = models.DateTimeField()
    package = models.ForeignKey(Packages, on_delete=models.DO_NOTHING)
    package_period_start = models.DateTimeField()
    package_period_end = models.DateTimeField()
    package_name = models.CharField(max_length=200)
    package_rent_per_month = models.FloatField()
    package_amount = models.FloatField()
    renewal_amount = models.FloatField()
    package_cycle = models.IntegerField()
    contact_persons = models.TextField()
    renewal_status = models.IntegerField(
        default=1,
        choices=[
            (0, "Do not show to renewal team"),
            (1, "Show to renewal team"),
        ],
    )
    retention_status = models.IntegerField(
        default=0,
        choices=[
            (0, "Do not show to retention team"),
            (1, "Show to retention team"),
        ],
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)
    objects = models.Manager()

    class Meta:
        db_table = "renew_retains"


class ActivityNotes(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    service = models.ForeignKey(Services, on_delete=models.DO_NOTHING)
    renew_retain = models.ForeignKey(
        RenewRetains, on_delete=models.DO_NOTHING, default=None, null=True
    )
    note = models.TextField()
    created_by = models.ForeignKey(
        UserProfiles, on_delete=models.DO_NOTHING, db_column="created_by"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "activity_notes"
        indexes = [
            models.Index(fields=["service_id"]),
            models.Index(fields=["billing_account_id"]),
            models.Index(fields=["created_by"]),
            models.Index(fields=["created"]),
        ]


class ActivityActions(models.Model):
    id = models.AutoField(primary_key=True)
    event_action = models.ForeignKey(EventActions, on_delete=models.CASCADE)
    event_reasons = models.ForeignKey(EventReasons, on_delete=models.CASCADE)
    activity_note = models.ForeignKey(
        ActivityNotes, on_delete=models.CASCADE, default=None, null=True
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    renew_retain = models.ForeignKey(
        RenewRetains, on_delete=models.CASCADE, null=True
    )
    created_by = models.ForeignKey(
        UserProfiles, on_delete=models.CASCADE, db_column="created_by"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "activity_actions"
        indexes = [
            models.Index(fields=["event_action_id"]),
            models.Index(fields=["event_reasons_id"]),
            models.Index(fields=["activity_note_id"]),
        ]


class FeatureUsagesLogs(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    package_feature = models.ForeignKey(
        PackageFeatures, on_delete=models.DO_NOTHING
    )
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    leg_a_use = models.IntegerField(help_text="units only")
    leg_b_min = models.IntegerField(default=0)
    leg_b_rate = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()
    entries = managers.FeatureUsageManager()

    class Meta:
        db_table = "feature_usages_logs"
        indexes = [
            models.Index(
                fields=["package_feature_id", "service_id", "start_date"]
            ),
            models.Index(fields=["service_id"]),
            models.Index(fields=["package_feature_id"]),
        ]

    def __str__(self) -> str:
        return str(self.id)


class ResourceCharges(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.CASCADE)
    resource = models.CharField(
        max_length=50,
        choices=ResourceTypeEnums.choices,
        default=ResourceTypeEnums.campaign.value,
    )
    resource_id = models.CharField(max_length=50, unique=True)
    estimated_amount = models.DecimalField(
        max_digits=10, decimal_places=3, default=0
    )
    actual_amount = models.DecimalField(
        max_digits=10, decimal_places=3, null=True, blank=True
    )
    is_refunded = models.BooleanField(default=False)
    refunded_at = models.DateTimeField(null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "resource_charges"

    def __str__(self) -> str:
        return str(self.id)

    def mark_as_refunded(self):
        self.refunded_at = timezone.now()
        self.is_refunded = True
        self.save()

    def update_actual_amount(self, actual_amount):
        self.actual_amount = actual_amount
        self.save()


class FeatureActivationRequest(models.Model):
    id = models.CharField(max_length=50, primary_key=True, default=uuid)
    service = models.ForeignKey(Services, on_delete=models.DO_NOTHING)
    product_feature = models.ForeignKey(
        ProductFeatures, on_delete=models.DO_NOTHING
    )
    payment = models.OneToOneField(
        Recharges,
        on_delete=models.DO_NOTHING,
        unique=True,
        null=True,
        default=None,
    )
    is_credit = models.BooleanField(default=False)
    ref_id = models.CharField(max_length=50, null=True, blank=True)
    status = models.SmallIntegerField(
        choices=FeatureActivationRequestStatusEnums.choices(),
        default=FeatureActivationRequestStatusEnums.PENDING.value,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "feature_activation_requests"
