import typing as t
from datetime import datetime
from decimal import Decimal
from django.db import models
from django.db.models import Q, Sum, DecimalField
from django.db.models.functions import Coalesce
from .enums import ServiceRentalStatusEnum, ServiceNumberPaidEnum
from accounts.billing_accounts.models import BillingAccounts

if t.TYPE_CHECKING:
    from accounts.services.models import Services, FeatureUsagesLogs
    from accounts.packages.models import PackageFeatures


class ServiceQuerySet(models.QuerySet):
    def product(self, product_id: str):
        return self.filter(product_id=product_id)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ) -> "models.QuerySet":
        """
        Filters the queryset based on the given billing account.

        If `is_corporate` is not specified, it will be determined based on whether
        the billing account is a parent billing account number (BAN). If the account
        is corporate, the queryset is filtered to include services linked to the
        parent billing account. Otherwise, it filters services associated with the
        specified billing account.

        Args:
            billing_account (BillingAccounts): The billing account to filter by.
            is_corporate (bool, optional): Specifies if the account is corporate.
                If not provided, it is automatically determined.

        Returns:
            models.QuerySet: A queryset filtered by the billing account.
        """
        if is_corporate is None and billing_account.is_parent_ban():
            is_corporate = True

        if is_corporate:
            query_set = self.filter(billing_account__parent=billing_account)
        else:
            query_set = self.filter(billing_account=billing_account)

        return query_set

    def total_current_usage(self) -> Decimal:
        return self.aggregate(
            current_usage=Coalesce(
                Sum("current_usages"), 0, output_field=DecimalField()
            )
        )["current_usage"]


class ServiceRentalQuerySet(models.QuerySet):
    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ) -> "models.QuerySet":
        """
        Filters the queryset based on the given billing account.

        If `is_corporate` is not specified, it will be determined based on whether
        the billing account is a parent billing account number (BAN). If the account
        is corporate, the queryset is filtered to include service rentals linked
        to the parent billing account. Otherwise, it filters service rentals
        associated with the specified billing account.

        Args:
            billing_account (BillingAccounts): The billing account to filter by.
            is_corporate (bool, optional): Specifies if the account is corporate.
                If not provided, it is automatically determined.

        Returns:
            models.QuerySet: A queryset filtered by the billing account.
        """
        if is_corporate is None and billing_account.is_parent_ban():
            is_corporate = True

        if is_corporate:
            query_set = self.filter(
                service__billing_account__parent=billing_account
            )
        else:
            query_set = self.filter(service__billing_account=billing_account)

        return query_set

    def service(self, service_id) -> "models.QuerySet":
        return self.filter(service_id=service_id)

    def active(self) -> "models.QuerySet":
        return self.filter(status=ServiceRentalStatusEnum.ACTIVE.value).first()

    def total_pending_rental(self) -> Decimal:
        """
        Calculates the total pending rental for the queryset.

        Only active rows are considered when calculating the total.

        Returns:
            Decimal: The total pending rental.
        """
        qs = self.filter(status=ServiceRentalStatusEnum.ACTIVE.value)
        return qs.aggregate(
            total_pending_rental=Coalesce(
                Sum("pending_rental"), 0, output_field=DecimalField()
            )
        )["total_pending_rental"]


class ServicePackageQuerySet(models.QuerySet):
    def service(self, service_id) -> "models.QuerySet":
        return self.filter(service_id=service_id)

    def active(self, time) -> "models.QuerySet":
        return self.filter(start_time__lte=time, end_time__gte=time).first()

    def active_packages(self, time) -> "models.QuerySet":
        return self.filter(start_time__lte=time, end_time__gte=time)


class OtherChargesQuerySet(models.QuerySet):
    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ) -> "models.QuerySet":
        """
        Filters the queryset based on the given billing account.

        If `is_corporate` is not specified, it will be determined based on whether
        the billing account is a parent billing account number (BAN). If the account
        is corporate, the queryset is filtered to include service rentals linked
        to the parent billing account. Otherwise, it filters service rentals
        associated with the specified billing account.

        Args:
            billing_account (BillingAccounts): The billing account to filter by.
            is_corporate (bool, optional): Specifies if the account is corporate.
                If not provided, it is automatically determined.

        Returns:
            models.QuerySet: A queryset filtered by the billing account.
        """
        if is_corporate is None and billing_account.is_parent_ban():
            is_corporate = True

        if is_corporate:
            query_set = self.filter(
                service__billing_account__parent=billing_account
            )
        else:
            query_set = self.filter(service__billing_account=billing_account)

        return query_set

    def total_other_charges(self) -> Decimal:
        """
        Calculates the total other charges in the queryset.

        Returns:
            Decimal: The total other charges.
        """
        return self.aggregate(
            other_charge=Coalesce(Sum("charge"), 0, output_field=DecimalField())
        )["other_charge"]


class ServiceNumberQuerySet(models.QuerySet):
    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ) -> "models.QuerySet":
        """
        Filters the queryset based on the given billing account.

        If `is_corporate` is not specified, it will be determined based on whether
        the billing account is a parent billing account number (BAN). If the account
        is corporate, the queryset is filtered to include service numbers linked
        to the parent billing account. Otherwise, it filters service numbers
        associated with the specified billing account.

        Args:
            billing_account (BillingAccounts): The billing account to filter by.
            is_corporate (bool, optional): Specifies if the account is corporate.
                If not provided, it is automatically determined.

        Returns:
            models.QuerySet: A queryset filtered by the billing account.
        """
        if is_corporate is None and billing_account.is_parent_ban():
            is_corporate = True

        if is_corporate:
            query_set = self.filter(
                service__billing_account__parent=billing_account
            )
        else:
            query_set = self.filter(service__billing_account=billing_account)

        return query_set

    def total_unpaid_number_cost(self) -> Decimal:
        """
        Calculates the total unpaid number cost for the queryset.

        Only service numbers that are not paid are considered when calculating the total.

        Returns:
            Decimal: The total unpaid number cost.
        """
        return self.filter(
            is_paid=ServiceNumberPaidEnum.UNPAID.value
        ).aggregate(
            number_cost=Coalesce(
                Sum("number_cost"), 0, output_field=DecimalField()
            )
        )[
            "number_cost"
        ]


class FeatureUsageQuerySet(models.QuerySet):
    def service(self, service: "Services") -> "models.QuerySet":
        return self.filter(service=service)

    def find_max_leg_a_usage_instance(
        self,
        service: "Services",
        package_feature: "PackageFeatures",
        start_date: datetime,
    ) -> "FeatureUsagesLogs":
        return (
            self.service(service)
            .filter(
                package_feature=package_feature,
                start_date=start_date,
            )
            .order_by("-leg_a_use")
            .first()
        )
