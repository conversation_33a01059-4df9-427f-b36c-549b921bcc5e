import logging
from django.utils import timezone
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceActivationFailedException,
)
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.utils.api_services.number_system import NumberSystem
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
    FraudBillingAccountException,
)
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services, ServiceNumbers
from accounts.services.constants import (
    SERVICE_STATUS_INACTIVE,
    SERVICE_NUMBER_STATUS_INACTIVE,
)
from accounts.payments.models import Recharges, TrackingSettlementHistories
from django.db import transaction

logger = logging.getLogger(__name__)


def service_activation(ban_id, payment_id):
    """
    Activates a service for a billing account.

    Args:
        ban_id (int): The ID of the billing account.
        payment_id (int): The ID of the payment.

    Returns:
        str: The result of the service activation.

    Raises:
        InvalidBillingAccountException: If the billing account is not found.
        FraudBillingAccountException: If the billing account is flagged as fraud.
        InvalidServiceException: If the service is not found or is not a demo service.
        ServiceActivationFailedException: If the payment is invalid or not settled, or if the service activation fails.
    """
    logger.title("Service Activation for Billing Account:").info(ban_id)

    billing_account = BillingAccounts.objects.filter(id=ban_id).first()
    if billing_account is None:
        raise InvalidBillingAccountException("Billing Account not found")

    if billing_account.is_fraud():
        raise FraudBillingAccountException("Billing Account is fraud")

    service = Services.objects.filter(billing_account_id=ban_id).first()
    if not service:
        raise InvalidServiceException("Service not found")

    if not service.is_demo():
        raise InvalidServiceException("Not a demo service")

    payment_settlement_history = TrackingSettlementHistories.objects.filter(
        payment_id=payment_id
    ).exists()
    if not payment_settlement_history:
        raise ServiceActivationFailedException("Invalid Payment or not settled")
    
    """
    Current Process: (needs to be fixed in future)
    service number and package is allocated by `auto_claim_settle` API
    
    Depedency: myoperator service number and package allocation
    """

    result = AccountApiV1().initiate_activation(service.gsn, payment_id)
    if not result:
        raise ServiceActivationFailedException("Service activation failed")

    return result
