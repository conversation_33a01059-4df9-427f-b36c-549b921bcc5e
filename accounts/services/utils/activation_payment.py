import logging
from accounts.services.models import Services, ServiceNumbers
from accounts.utils.api_services.payment_service import PaymentService
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.services.exceptions import ServiceAlreadyRunningException
from accounts.products.utils import get_country_from_product_id
from accounts.cafs.utils.caf import is_caf_approved
from accounts.utils.api_services.number_system import NumberSystem
from accounts.cafs.exceptions import CafNotApprovedException
from accounts.billing_accounts.utils.service_contact import (
    get_billing_contact_person,
)

from typing import Any, Dict

logger = logging.getLogger(__name__)


def generate_activation_payment_link(
    gsn: str, package_id: str, service_number: str, **kwargs: Any
) -> Dict[str, Any]:
    """
    Generates a payment link for a service activation.
    Args:
        gsn (str): The GSN (Global Service Number) of the service.
        package_id (str): The ID of the package.
        service_number (str): The service number.
        **kwargs (Any): Additional keyword arguments.
    Returns:
        Dict[str, Any]: The generated payment data.
    Raises:
        ServiceAlreadyRunningException: If the service is already running with the service number.
    """

    service = Services.objects.get(gsn=gsn)
    # Check if service is already running with the service number on some other service
    if is_service_running(service_number, service.id):
        logger.error(
            f"Service is already running on service number {service_number}"
        )
        raise ServiceAlreadyRunningException()

    if not is_caf_approved(service.billing_account.id):
        raise CafNotApprovedException()

    number_details = NumberSystem().get_number_details(gsn, service_number)

    country = get_country_from_product_id(service.product_id)

    # fetch contact details for the payment
    contact_info = get_billing_contact_person(service.billing_account.id)
    name = kwargs.get("name") or contact_info.get("name")
    email = kwargs.get("email") or contact_info.get("email")
    phone = kwargs.get("phone") or contact_info.get("mobile")

    if "amount" in kwargs and kwargs["amount"]:
        amount = kwargs["amount"]
    else:
        logger.title("Generate payment activation link").info(
            "Custom amount is not available, calculating payable amount"
        )
        amount = fetch_payable_amount(gsn, package_id)

    # create payment data for generating payment URL
    payment_data = PaymentService().generate_payment_url(
        billing_account_id=service.billing_account_id,
        ac_number=service.billing_account.ac_number,
        amount=amount,
        country_id=country.country_id,
        state_id=service.billing_account.state_id,
        name=name,
        email=email,
        phone=phone,
        request_of="activation",
        package_id=package_id,
        service_number=service_number,
        number_cost=number_details.get("number_cost"),
        number_type=number_details.get("number_type"),
        response_url=kwargs.get("redirect_url"),
        payment_method=kwargs.get("payment_method"),
    )
    return payment_data["payment_id"], payment_data["url"]


def is_service_running(service_number: str, service_id: str) -> bool:
    return (
        ServiceNumbers.objects.filter(service_number=service_number, status=1)
        .exclude(service_id=service_id)
        .exists()
    )


def fetch_payable_amount(gsn: str, package_id: str) -> float:
    payable_data = AccountApiV1().payable_amount(gsn, package_id)
    return payable_data["payable_amount"]
