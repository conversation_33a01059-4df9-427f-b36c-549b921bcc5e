from django.test import TestCase
import pytest

from unittest.mock import patch

from accounts.services.utils.activation_payment import (
    generate_activation_payment_link,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
)
from accounts.services.exceptions import ServiceAlreadyRunningException
from accounts.cafs.exceptions import CafNotApprovedException
from accounts.cafs.tests.factories import CafFactory


class TestActivationPayment(TestCase):
    def setUp(self):
        pass

    def test_generate_activation_payment_link_service_already_running(self):
        service = ServiceFactory.create()
        service_number = ServiceNumberFactory.create()
        with pytest.raises(ServiceAlreadyRunningException):
            generate_activation_payment_link(
                gsn=service.gsn,
                package_id="********",
                service_number=service_number.service_number,
            )

    def test_generate_activation_payment_link_caf_not_approved(self):
        service = ServiceFactory.create()
        ServiceNumberFactory.create()
        with pytest.raises(CafNotApprovedException):
            generate_activation_payment_link(
                gsn=service.gsn,
                package_id="********",
                service_number="********",
            )

    @patch("accounts.utils.api_services.account_v1.AccountApiV1.payable_amount")
    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    def test_generate_activation_payment_link_success(
        self, mock_payment_url, mock_number_details, mock_payable_amount
    ):
        mock_payable_amount.return_value = {"payable_amount": 500}
        mock_number_details.return_value = {"number_cost": 50, "number_type": 2}
        mock_payment_url.return_value = {
            "url": "http://abc.com/test-payment",
            "payment_id": "xyz123",
        }
        service = ServiceFactory.create()
        CafFactory.create(billing_account=service.billing_account)
        payment_id, url = generate_activation_payment_link(
            gsn=service.gsn,
            package_id="********",
            service_number="9999",
            name="Shenoy",
            email="<EMAIL>",
            phone="**********",
            redirect_url="http://abc.com/example",
            payment_method=1,
        )
        assert url == "http://abc.com/test-payment"
        assert payment_id == "xyz123"
