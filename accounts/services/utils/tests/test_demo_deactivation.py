from unittest.mock import patch
from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from django.conf import settings
import pytest
from freezegun import freeze_time
from accounts.core.tests.factories import CountryFactory
from accounts.packages.tests.factories import (
    PackageFactory,
)
from accounts.global_packages.tests.factories import (
    PackageCategoryFactory,
)
from accounts.products.tests.factories import ProductFactory
from accounts.services.exceptions import InvalidServiceException
from accounts.services.models import ServiceNumbers
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.utils.service_deactivation import deactivate_demo_service


class TestAccountDeactivationUtils(TestCase):
    def setUp(self):

        self.country = CountryFactory.create()
        self.product = ProductFactory.create(country=self.country)
        self.country2 = CountryFactory.create()
        self.product2 = ProductFactory.create(country=self.country2)
        self.service = ServiceFactory.create(
            product=self.product,
            country=self.country,
            user_profile=None,
        )
        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="1234",
        )
        self.package_category = PackageCategoryFactory.create(
            product=self.product,
            code="abcd",
        )
        self.package = PackageFactory.create(
            product=self.product,
            package_custom=None,
            discount=None,
            package_category=self.package_category,
        )

        self.service_package = ServicePackageFactory.create(
            service_id=self.service.id,
            package_id=self.package.id,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(days=30),
        )

        self.user_profile = UserProfileFactory(id=settings.DD_CRON)

        self.setting_key = f"SERVICE_NUMBER_ARCHIVE_DAYS_{self.service.live_status}_{self.package_category.code}"

    @freeze_time(timezone.now())
    @patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @patch("accounts.services.events.ServiceDeactivationEvent.send")
    def test_deactivate_service_success(
        self,
        mock_send,
        mock_memcache,
        mock_deactivate_service_number,
        mock_deactivate_company,
    ):
        mock_send.return_value = True
        mock_memcache.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_deactivate_company.return_value = True

        deactivate_demo_service(self.service.id, self.user_profile.id)

        self.service.refresh_from_db()
        self.assertEqual(self.service.status, 0)
        self.assertEqual(self.service.churn_date, timezone.now())
        self.service_package.refresh_from_db()
        self.assertEqual(self.service_package.end_time, timezone.now())
        service_number = ServiceNumbers.objects.get(id=self.service_number.id)
        self.assertIsNotNone(service_number.archive_date)
        self.assertEqual(service_number.status, 0)
        self.assertEqual(service_number.is_archived, 0)
        mock_send.assert_called_once()
        mock_memcache.assert_called_once()
        mock_deactivate_service_number.asser_called_once()
        mock_deactivate_company.assert_called_once()

    def test_deactivate_demo_invalid_service_exception(self):
        # Invalid Service
        with pytest.raises(InvalidServiceException):
            deactivate_demo_service("12345", self.user_profile.id)

        # Non demo service
        with pytest.raises(InvalidServiceException):
            service = ServiceFactory.create(live_status=2)
            deactivate_demo_service(service.id, self.user_profile.id)
