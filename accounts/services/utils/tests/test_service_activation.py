import logging
import pytest
from django.test import TestCase
from unittest import mock
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceActivationFailedException,
)
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
    FraudBillingAccountException,
)
from accounts.payments.tests.factories import (
    RechargesFactory,
    TrackingSettlementHistoriesFactory,
    PaymentTracksFactory,
)
from accounts.services.tests.factories import ServiceFactory
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.services.utils.service_activation import service_activation

logger = logging.getLogger(__name__)


class TestServiceActivation(TestCase):
    def setUp(self):
        self.recharge = RechargesFactory.create(
            payment_id="abc_123_333", request_of="activation"
        )
        self.service = ServiceFactory.create(
            billing_account=self.recharge.billing_account,
        )
        self.payment_track = PaymentTracksFactory.create(
            payment_id=self.recharge.payment_id, amount=self.recharge.amount
        )
        self.payment_settlement_history = (
            TrackingSettlementHistoriesFactory.create(
                billing_account=self.recharge.billing_account,
                setl_key=self.payment_track.txn_setl_key,
                payment=self.payment_track,
                amount=self.payment_track.amount,
            )
        )

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.initiate_activation"
    )
    def test_service_activation_successful(self, mock_api):
        mock_api.return_value = True
        result = service_activation(
            self.recharge.billing_account.id, self.recharge.payment_id
        )
        assert result is True

    def test_service_activation_invalid_billing_account(self):
        with pytest.raises(InvalidBillingAccountException) as excinfo:
            service_activation("ban123", "payment123")
        assert str(excinfo.value) == "Billing Account not found"

    def test_service_activation_froud_billing_account(self):
        billing_account = BillingAccountFactory.create(verification_state=5)
        with pytest.raises(FraudBillingAccountException) as excinfo:
            service_activation(billing_account.id, "payment123")
        assert str(excinfo.value) == "Billing Account is fraud"

    def test_service_activation_invalid_service(self):
        billing_account = BillingAccountFactory.create()
        with pytest.raises(InvalidServiceException) as excinfo:
            service_activation(billing_account.id, "payment123")
        assert str(excinfo.value) == "Service not found"

    def test_service_activation_non_demo_service(self):
        recharge = RechargesFactory.create(
            payment_id="abc_333", request_of="activation"
        )
        ServiceFactory.create(
            billing_account=recharge.billing_account, live_status=1
        )
        with pytest.raises(InvalidServiceException) as excinfo:
            service_activation(recharge.billing_account.id, recharge.payment_id)
        assert str(excinfo.value) == "Not a demo service"

    def test_service_activation_payment_not_settled(self):
        recharge = RechargesFactory.create(
            payment_id="abc_333", request_of="activation"
        )
        ServiceFactory.create(
            billing_account=recharge.billing_account, status=0
        )
        with pytest.raises(ServiceActivationFailedException) as excinfo:
            service_activation(recharge.billing_account.id, recharge.payment_id)
        assert str(excinfo.value) == "Invalid Payment or not settled"

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.initiate_activation"
    )
    def test_service_activation_failed(self, mock_initiate_activation):
        mock_initiate_activation.return_value = False
        with pytest.raises(ServiceActivationFailedException) as excinfo:
            service_activation(
                self.recharge.billing_account.id, self.recharge.payment_id
            )
        assert str(excinfo.value) == "Service activation failed"
