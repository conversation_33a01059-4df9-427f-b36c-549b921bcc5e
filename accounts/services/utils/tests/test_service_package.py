from django.test import TestCase
from django.utils import timezone
from faker import Faker
from accounts.packages.tests.factories import PackageFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServicePackageFactory,
)
from accounts.services.utils.service_package import get_current_service_package


class TestServicePackage(TestCase):
    def setUp(self):
        self.fake = Faker()

        self.expiry_datetime = timezone.now() + timezone.timedelta(days=365)
        self.start_date = timezone.now() - timezone.timedelta(days=30)
        self.end_date = timezone.now() - timezone.timedelta(days=2)

        self.service = ServiceFactory.create(user_profile=None)
        self.package = PackageFactory.create(
            package_custom=None,
            discount=None,
        )
        self.service_package = ServicePackageFactory.create(
            service_id=self.service.id,
            package_id=self.package.id,
            start_time=self.start_date,
            end_time=self.expiry_datetime,
        )

    def test_get_current_service_package_success(self):
        service_package = get_current_service_package(self.service.id)
        self.assertEqual(service_package.id, self.service_package.id)

    def test_get_current_service_package_failure(self):
        service_package = get_current_service_package(12345)
        self.assertFalse(service_package)
