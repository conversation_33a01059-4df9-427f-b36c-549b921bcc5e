from django.test import TestCase
from django.utils import timezone
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.models import ServiceNumbers
from accounts.services.tests.factories import (
    ServiceFactory,
    ActivityNoteFactory,
)
from accounts.services.utils.activity_note import (
    add_activity_notes,
    get_activity_note,
)


class TestActivityNote(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create()

        self.service_number = ServiceNumbers.objects.create(
            service_id=self.service.id,
            service_number="**********",
            number_cost=100,
            is_paid=1,
            status=0,
        )

        self.user_profile = UserProfileFactory()

        self.activity = ActivityNoteFactory(
            billing_account=self.service.billing_account,
            service_id=self.service.id,
            note="test note",
            created_by=self.user_profile,
        )

    def test_get_activity_note_false(self):
        assert (
            get_activity_note(
                self.service.id,
                "test note",
                activity_date="2020-10-10",
                created_by="123",
            )
            is False
        )

    def test_get_activity_note_true(self):
        result = get_activity_note(
            self.service.id,
            note="test note",
            activity_date=timezone.now(),
            created_by=self.user_profile,
        )

        assert result.billing_account.id == self.service.billing_account.id
        assert result.service_id == self.service.id

    def test_add_activity_notes_success(self):
        note = "test note"
        created_by = self.user_profile.id
        result = add_activity_notes(
            self.service.billing_account.id, self.service.id, note, created_by
        )

        assert result.billing_account.id == self.service.billing_account.id
        assert result.service_id == self.service.id
        assert result.note == note
        assert result.created_by == self.user_profile
