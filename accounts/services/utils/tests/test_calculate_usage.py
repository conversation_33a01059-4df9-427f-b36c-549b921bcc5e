from decimal import Decimal
from django.test import TestCase

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    ServiceNumberFactory,
    OtherChargesFactory,
)
from accounts.discounts.tests.factories import DiscountBucketsFactory
from accounts.services.utils.pending_usage import calculate_usage
from accounts.services.cache_handler import PendingUsageCacheHandler


class TestCalculateUsage(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()
        self.service = ServiceFactory.create(
            billing_account=self.billing_account, current_usages=100, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=500, service=self.service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=self.service, status=1, is_paid=0
        )
        OtherChargesFactory.create(charge=150, service=self.service, status=1)
        DiscountBucketsFactory.create(
            billing_account=self.billing_account,
            apply_on="R",
            status=1,
            value=300,
        )
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=-200, status=1
        )

    def test_calculate_usage_success(self):
        result = calculate_usage(self.billing_account)
        assert result["pending_rental"] == Decimal("500")
        assert result["pending_usages"] == Decimal("200")
        assert result["current_usages"] == Decimal("100")
        assert result["other_charges"] == Decimal("150")
        assert result["service_number_cost"] == Decimal("300")
        assert result["discount"] == Decimal("300")
        assert result["advance"] == Decimal("0")
        assert result["available"] == Decimal("-950")

    def test_calculate_usage_for_corporate_ban(self):
        parent_ban = BillingAccountFactory.create()
        self.billing_account.parent = parent_ban
        self.billing_account.save()

        # Credits are managed seperatly for Corporate BAN
        BillingAccountCreditsFactory.create(
            billing_account=parent_ban, credit_amount=-200, status=1
        )
        DiscountBucketsFactory.create(
            billing_account=self.billing_account.parent,
            apply_on="R",
            status=1,
            value=300,
        )

        result = calculate_usage(parent_ban)
        assert result["pending_rental"] == Decimal("500")
        assert result["pending_usages"] == Decimal("200")
        assert result["current_usages"] == Decimal("100")
        assert result["other_charges"] == Decimal("150")
        assert result["service_number_cost"] == Decimal("300")
        assert result["discount"] == Decimal("300")
        assert result["advance"] == Decimal("0")
        assert result["available"] == Decimal("-950")

    def test_calulate_usage_for_inactive_service(self):
        self.service.status = 0
        self.service.save()
        result = calculate_usage(self.billing_account)
        assert result["pending_rental"] == Decimal("500")
        assert result["pending_usages"] == Decimal("200")
        assert result["current_usages"] == Decimal("100")
        assert result["other_charges"] == Decimal("150")
        assert result["service_number_cost"] == Decimal("300")
        assert result["discount"] == Decimal("300")
        assert result["advance"] == Decimal("0")
        assert result["available"] == Decimal("-950")
