import logging
from accounts.services.models import (
    Services,
    ServiceRentals,
    OtherCharges,
    ServiceNumbers,
)
from accounts.services.exceptions import (
    InvalidServiceException,
)
from accounts.billing_accounts.models import (
    BillingAccounts,
    DiscountBuckets,
    BillingAccountCredits,
)

from accounts.discounts.enums import DiscountAppliedOnEnum
from accounts.services.cache_handler import PendingUsageCacheHandler
from typing import Dict, Any, Union

logger = logging.getLogger(__name__)


def get_pending_usage(service: "Services") -> Dict[str, Any]:
    """
    Calculates and returns the pending usage for a given service.

    Args:
        service (Services): The service object for which the pending usage is calculated.

    Returns:
        Dict[str, Any]: A dictionary containing the calculated pending usage data. The dictionary
        includes the currency of the service's product.

    Raises:
        None

    Side Effects:
        Deletes and saves the calculated usage data in the PendingUsageCacheHandler.

    """
    calc_data = calculate_usage(service.billing_account)
    logger.title("#calculate_usage result").info(calc_data)
    calc_data.update({"currency": service.product.currency})

    cache_handler = PendingUsageCacheHandler(service.billing_account)
    cache_handler.delete()
    cache_handler.save(calc_data)
    return calc_data


def calculate_usage(billing_account: BillingAccounts) -> Dict[str, Any]:
    total_pending = pending_usages = discount_amt = 0

    billing_account, is_parent = billing_account.parent_ban()

    pending_rental = ServiceRentals.entries.billing_account(
        billing_account, is_parent
    ).total_pending_rental()
    current_usages = Services.objects.billing_account(
        billing_account, is_parent
    ).total_current_usage()
    pending_other_charges = OtherCharges.unpaid.billing_account(
        billing_account, is_parent
    ).total_other_charges()
    number_cost = ServiceNumbers.entries.billing_account(
        billing_account, is_parent
    ).total_unpaid_number_cost()

    advance = credit_amount = BillingAccountCredits.entries.billing_account(
        billing_account
    ).credit_amount()
    if credit_amount < 0:
        pending_usages = abs(credit_amount)
        advance = 0

    total_pending = (
        pending_rental
        + pending_other_charges
        + number_cost
        + current_usages
        + pending_usages
    )

    discount = DiscountBuckets.active.billing_account(
        billing_account
    ).get_discount()
    if discount and discount.value > 0:
        if discount.apply_on == DiscountAppliedOnEnum.RENTAL.value:
            discount_amt = discount.discount_amount(pending_rental)
        elif discount.apply_on == DiscountAppliedOnEnum.USAGES.value:
            discount_amt = discount.discount_amount(current_usages)
        elif discount.apply_on == DiscountAppliedOnEnum.ALL.value:
            discount_amt = discount.discount_amount(total_pending)

    if total_pending > 0 and discount_amt > 0:
        total_pending = total_pending - discount_amt

    available = advance - total_pending

    return {
        "pending_rental": pending_rental,
        "pending_usages": pending_usages,
        "current_usages": current_usages,
        "other_charges": pending_other_charges,
        "service_number_cost": number_cost,
        "discount": discount_amt,
        "advance": advance,
        "available": available,
    }
