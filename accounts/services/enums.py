from django.db import models
from accounts.enums import BaseEnum


class ServiceStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1
    SUSPENDED = 2


class ServiceLiveStatusEnum(BaseEnum):
    POSTPAID = 1
    PREPAID = 2
    DEMO = 3


class ServiceRentalStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class OtherChargesStatusEnum(BaseEnum):
    PAID = 0
    UNPAID = 1


class ServiceNumberStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ServiceNumberPaidEnum(BaseEnum):
    UNPAID = 0
    PAID = 1


class ServiceNumberArchiveEnum(BaseEnum):
    FALSE = 0
    TRUE = 1


class ResourceTypeEnums(models.TextChoices):
    campaign = "campaign", "campaign"


class FeatureActivationRequestStatusEnums(BaseEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2


class ServiceNumberTypeEnums(BaseEnum):
    LANDLINE = 1
    MOBILE = 2
    TOLLFREE = 3
    HEYO = 4
