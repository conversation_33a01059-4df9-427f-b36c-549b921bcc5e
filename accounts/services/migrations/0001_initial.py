# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing_accounts', '0001_initial'),
        ('users', '0001_initial'),
        ('core', '0001_initial'),
        ('products', '0001_initial'),
        ('payments', '0001_initial'),
        ('packages', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventActions',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('team_type', models.CharField(default='renewal', max_length=50)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'event_actions',
            },
        ),
        migrations.CreateModel(
            name='Services',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('gsn', models.CharField(max_length=200, unique=True)),
                ('timezone', models.CharField(default='00:00', max_length=200)),
                ('rent_per_month', models.FloatField(default=0.0)),
                ('renew_cycle', models.IntegerField()),
                ('activation_date', models.DateTimeField(null=True)),
                ('expiry_date', models.DateTimeField()),
                ('last_renewal', models.DateTimeField(null=True)),
                ('live_status', models.IntegerField(default=3, help_text='1 => post paid, 2 => prepaid, 3 => demo, 6 => mobile sync')),
                ('status', models.IntegerField(default=1, help_text='0 => inactive , 1 => active, 2=>suspended, 3 => temporary suspend due to activation date change')),
                ('churn_date', models.DateTimeField(null=True)),
                ('comment', models.TextField(null=True)),
                ('current_usages', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service', to='billing_accounts.billingaccounts')),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='core.countries')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='products.products')),
                ('user_profile', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='users.userprofiles')),
            ],
            options={
                'db_table': 'services',
            },
        ),
        migrations.CreateModel(
            name='ServiceRentals',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('rental_amount', models.FloatField(default=0.0)),
                ('pending_rental', models.FloatField(default=0.0)),
                ('trans_type', models.CharField(max_length=10)),
                ('status', models.SmallIntegerField(default=1)),
                ('date', models.DateField()),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'service_rentals',
            },
        ),
        migrations.CreateModel(
            name='ServicePackages',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('package_type', models.CharField(default='main', help_text='main => package, addon => addon package', max_length=5)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='packages.packages')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'service_packages',
            },
        ),
        migrations.CreateModel(
            name='ServiceNumbers',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('service_number', models.CharField(max_length=50)),
                ('number_type', models.CharField(help_text='1:landline, 2:mobile, 3:tollfree', max_length=20)),
                ('number_cost', models.IntegerField()),
                ('is_paid', models.SmallIntegerField()),
                ('is_live', models.SmallIntegerField(default=0)),
                ('is_archived', models.SmallIntegerField(default=0)),
                ('archive_date', models.DateTimeField(null=True)),
                ('swapped_date', models.DateTimeField(null=True)),
                ('status', models.IntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'service_numbers',
            },
        ),
        migrations.CreateModel(
            name='ResourceCharges',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('resource', models.CharField(choices=[('campaign', 'campaign')], default='campaign', max_length=50)),
                ('resource_id', models.CharField(max_length=50, unique=True)),
                ('estimated_amount', models.DecimalField(decimal_places=3, default=0, max_digits=10)),
                ('actual_amount', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True)),
                ('is_refunded', models.BooleanField(default=False)),
                ('refunded_at', models.DateTimeField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'resource_charges',
            },
        ),
        migrations.CreateModel(
            name='RenewRetains',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('product_name', models.CharField(max_length=200)),
                ('product_currency', models.CharField(default='INR', max_length=20)),
                ('ban', models.CharField(max_length=30)),
                ('service_number', models.CharField(max_length=200)),
                ('business_name', models.CharField(max_length=250)),
                ('account_manager', models.CharField(blank=True, max_length=200, null=True)),
                ('account_manager_id', models.CharField(blank=True, max_length=36, null=True)),
                ('last_renewal', models.DateTimeField()),
                ('expiry_date', models.DateTimeField()),
                ('package_period_start', models.DateTimeField()),
                ('package_period_end', models.DateTimeField()),
                ('package_name', models.CharField(max_length=200)),
                ('package_rent_per_month', models.FloatField()),
                ('package_amount', models.FloatField()),
                ('renewal_amount', models.FloatField()),
                ('package_cycle', models.IntegerField()),
                ('contact_persons', models.TextField()),
                ('renewal_status', models.IntegerField(choices=[(0, 'Do not show to renewal team'), (1, 'Show to renewal team')], default=1)),
                ('retention_status', models.IntegerField(choices=[(0, 'Do not show to retention team'), (1, 'Show to retention team')], default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='packages.packages')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='products.products')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'renew_retains',
            },
        ),
        migrations.CreateModel(
            name='OtherCharges',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('charge', models.DecimalField(decimal_places=3, max_digits=10)),
                ('description', models.TextField(null=True)),
                ('status', models.SmallIntegerField()),
                ('date', models.DateField()),
                ('pay_date', models.DateTimeField(null=True)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'other_charges',
            },
        ),
        migrations.CreateModel(
            name='FeatureUsagesLogs',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('leg_a_use', models.IntegerField(help_text='units only')),
                ('leg_b_min', models.IntegerField(default=0)),
                ('leg_b_rate', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package_feature', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='packages.packagefeatures')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'feature_usages_logs',
            },
        ),
        migrations.CreateModel(
            name='FeatureActivationRequest',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=50, primary_key=True, serialize=False)),
                ('is_credit', models.BooleanField(default=False)),
                ('ref_id', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.SmallIntegerField(choices=[('PENDING', 0), ('SUCCESS', 1), ('FAILED', 2)], default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('payment', models.OneToOneField(default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='payments.recharges')),
                ('product_feature', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='products.productfeatures')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='services.services')),
            ],
            options={
                'db_table': 'feature_activation_requests',
            },
        ),
        migrations.CreateModel(
            name='EventReasons',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=250)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('event_action', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='services.eventactions')),
            ],
            options={
                'db_table': 'event_reasons',
            },
        ),
        migrations.CreateModel(
            name='ActivityNotes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('note', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('created_by', models.ForeignKey(db_column='created_by', on_delete=django.db.models.deletion.DO_NOTHING, to='users.userprofiles')),
                ('renew_retain', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='services.renewretains')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='services.services')),
            ],
            options={
                'db_table': 'activity_notes',
            },
        ),
        migrations.CreateModel(
            name='ActivityActions',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('activity_note', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='services.activitynotes')),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('created_by', models.ForeignKey(db_column='created_by', on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
                ('event_action', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.eventactions')),
                ('event_reasons', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.eventreasons')),
                ('renew_retain', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='services.renewretains')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services')),
            ],
            options={
                'db_table': 'activity_actions',
            },
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['created'], name='services_created_225aaa_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['user_profile_id'], name='services_user_pr_fbb6d3_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['status'], name='services_status_6676e7_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['live_status'], name='services_live_st_807e06_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['expiry_date'], name='services_expiry__43d551_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['activation_date'], name='services_activat_78eb69_idx'),
        ),
        migrations.AddIndex(
            model_name='services',
            index=models.Index(fields=['modified'], name='services_modifie_5a070c_idx'),
        ),
        migrations.AddConstraint(
            model_name='services',
            constraint=models.UniqueConstraint(fields=('gsn',), name='unique_gsn'),
        ),
        migrations.AddConstraint(
            model_name='services',
            constraint=models.UniqueConstraint(fields=('billing_account_id',), name='unique_billing_account_id'),
        ),
        migrations.AddIndex(
            model_name='servicerentals',
            index=models.Index(fields=['service_id'], name='service_ren_service_5fef98_idx'),
        ),
        migrations.AddIndex(
            model_name='servicerentals',
            index=models.Index(fields=['service_id', 'status'], name='service_ren_service_4513e6_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepackages',
            index=models.Index(fields=['service_id', 'start_time', 'end_time'], name='service_pac_service_599803_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepackages',
            index=models.Index(fields=['package_id'], name='service_pac_package_f4f75e_idx'),
        ),
        migrations.AddIndex(
            model_name='servicepackages',
            index=models.Index(fields=['end_time'], name='service_pac_end_tim_d499ba_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['service_id'], name='service_num_service_a48106_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['service_number'], name='service_num_service_ec5f54_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['number_cost'], name='service_num_number__92d04e_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['is_paid'], name='service_num_is_paid_6902ab_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['is_live'], name='service_num_is_live_c9e3d0_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['is_archived'], name='service_num_is_arch_262592_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['archive_date'], name='service_num_archive_925451_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['status'], name='service_num_status_8caec3_idx'),
        ),
        migrations.AddIndex(
            model_name='servicenumbers',
            index=models.Index(fields=['created'], name='service_num_created_9b566b_idx'),
        ),
        migrations.AddIndex(
            model_name='othercharges',
            index=models.Index(fields=['service_id', 'status', 'date'], name='other_charg_service_7a48e7_idx'),
        ),
        migrations.AddIndex(
            model_name='featureusageslogs',
            index=models.Index(fields=['package_feature_id', 'service_id', 'start_date'], name='feature_usa_package_3d0a2f_idx'),
        ),
        migrations.AddIndex(
            model_name='featureusageslogs',
            index=models.Index(fields=['service_id'], name='feature_usa_service_6bdcb6_idx'),
        ),
        migrations.AddIndex(
            model_name='featureusageslogs',
            index=models.Index(fields=['package_feature_id'], name='feature_usa_package_af756d_idx'),
        ),
        migrations.AddIndex(
            model_name='activitynotes',
            index=models.Index(fields=['service_id'], name='activity_no_service_40e2d8_idx'),
        ),
        migrations.AddIndex(
            model_name='activitynotes',
            index=models.Index(fields=['billing_account_id'], name='activity_no_billing_16c470_idx'),
        ),
        migrations.AddIndex(
            model_name='activitynotes',
            index=models.Index(fields=['created_by'], name='activity_no_created_201026_idx'),
        ),
        migrations.AddIndex(
            model_name='activitynotes',
            index=models.Index(fields=['created'], name='activity_no_created_623d8e_idx'),
        ),
        migrations.AddIndex(
            model_name='activityactions',
            index=models.Index(fields=['event_action_id'], name='activity_ac_event_a_2a74a1_idx'),
        ),
        migrations.AddIndex(
            model_name='activityactions',
            index=models.Index(fields=['event_reasons_id'], name='activity_ac_event_r_879b5d_idx'),
        ),
        migrations.AddIndex(
            model_name='activityactions',
            index=models.Index(fields=['activity_note_id'], name='activity_ac_activit_34378b_idx'),
        ),
    ]
