from django.urls import path
from accounts.services.views import (
    ServiceActivationView,
    ServiceActivationEventListenerView,
    ServiceListView,
    ServicePackageRetrieveAPIView,
    PackageSuggestionListView,
    CurrentServicePackageRetrieveAPIView,
    UpcomingServicePackageRetrieveAPIView,
    PendingUsageRetrieveAPIView,
    ResourceChargeCreateAPIView,
    FeatureActivationRequestCreateAPIView,
    FeatureActivateCreateAPIView,
    FeatureEventProcessView,
    ServiceReactivationEventProcessView,
    ServiceBillingDetailsView,
)

app_name = "services"

urlpatterns = [
    path(
        "",
        view=ServiceListView.as_view(),
        name="services_list",
    ),
    path(
        "<str:gsn>/initiate_activation",
        view=ServiceActivationView.as_view(),
        name="initiate_activation",
    ),
    path(
        "event/process_activation",
        view=ServiceActivationEventListenerView.as_view(),
        name="process_activation",
    ),
    path(
        "<str:gsn>/packages",
        view=ServicePackageRetrieveAPIView.as_view(),
        name="service_packages",
    ),
    path(
        "<str:gsn>/package_suggestion",
        view=PackageSuggestionListView.as_view(),
        name="package_suggestion",
    ),
    path(
        "<str:gsn>/packages/current",
        view=CurrentServicePackageRetrieveAPIView.as_view(),
        name="current_service_packages",
    ),
    path(
        "<str:gsn>/packages/upcoming",
        view=UpcomingServicePackageRetrieveAPIView.as_view(),
        name="upcoming_service_packages",
    ),
    path(
        "<str:gsn>/pending_usage",
        view=PendingUsageRetrieveAPIView.as_view(),
        name="pending_usage",
    ),
    path(
        "<str:gsn>/deduct_balance",
        view=ResourceChargeCreateAPIView.as_view(),
        name="deduct_balance",
    ),
    path(
        "<str:gsn>/feature_activation_request",
        view=FeatureActivationRequestCreateAPIView.as_view(),
        name="feature_activation_request",
    ),
    path(
        "<str:gsn>/billing_details",
        view=ServiceBillingDetailsView.as_view(),
        name="billing_details",
    ),
    path(
        "<str:gsn>/package/current/activate_features",
        view=FeatureActivateCreateAPIView.as_view(),
        name="feature_activate",
    ),
    path(
        "event/feature/process",
        view=FeatureEventProcessView.as_view(),
        name="process",
    ),
    path(
        "event/reactivation_process",
        view=ServiceReactivationEventProcessView.as_view(),
        name="ServiceReactivationEventProcessView",
    ),
]
