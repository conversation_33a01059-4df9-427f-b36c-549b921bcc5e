from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from django.test import TestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    OtherChargesFactory,
    ServiceNumberFactory,
    FeatureUsagesLogFactory,
)
from accounts.services.models import (
    Services,
    ServiceRentals,
    OtherCharges,
    ServiceNumbers,
    FeatureUsagesLogs,
)
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.services.enums import (
    OtherChargesStatusEnum,
    ServiceNumberPaidEnum,
    ServiceRentalStatusEnum,
)


class TestServiceQuerySet(TestCase):
    def setUp(self):
        self.parent_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.child_account1 = BillingAccountFactory(
            parent=self.parent_account, status=BillingStatusEnum.ACTIVE.value
        )
        self.service1 = ServiceFactory(billing_account=self.child_account1)
        self.child_account2 = BillingAccountFactory(
            parent=self.parent_account, status=BillingStatusEnum.INACTIVE.value
        )
        self.service2 = ServiceFactory(billing_account=self.child_account2)

    def test_billing_account_queryset(self):
        """Test fetching service of child account only"""
        qs = Services.objects.billing_account(self.child_account1)
        self.assertIn(self.service1, qs)
        self.assertNotIn(self.service2, qs)
        self.assertEqual(qs.count(), 1)

    def test_billing_account_queryset_with_corporate_ban(
        self,
    ):
        """Test fetching all services of parent account"""
        qs = Services.objects.billing_account(self.parent_account)
        self.assertIn(self.service1, qs)
        self.assertIn(self.service2, qs)
        self.assertEqual(qs.count(), 2)

    def test_total_current_usage(self):
        """Test total_current_usage calculation"""
        self.service1.current_usages = 50
        self.service1.save()
        self.service2.current_usages = 50
        self.service2.save()

        total_current_usage = Services.objects.total_current_usage()
        self.assertEqual(total_current_usage, 100)
        self.assertIsInstance(total_current_usage, Decimal)

        total_current_usage = Services.objects.filter(
            id=self.service1.id
        ).total_current_usage()
        self.assertEqual(total_current_usage, 50)
        self.assertIsInstance(total_current_usage, Decimal)


class TestServiceRentalQuerySet(TestCase):
    def setUp(self):
        self.parent_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.child_account1 = BillingAccountFactory(
            parent=self.parent_account, status=BillingStatusEnum.ACTIVE.value
        )
        self.service1 = ServiceFactory(billing_account=self.child_account1)
        self.service_rental1 = ServiceRentalFactory(
            service=self.service1, pending_rental=100
        )

        self.child_account2 = BillingAccountFactory(
            parent=self.parent_account, status=BillingStatusEnum.INACTIVE.value
        )
        self.service2 = ServiceFactory(billing_account=self.child_account2)
        self.service_rental2 = ServiceRentalFactory(
            service=self.service2, pending_rental=100
        )

    def test_billing_account_queryset(self):
        qs = ServiceRentals.objects.billing_account(self.child_account1)
        self.assertIn(self.service_rental1, qs)
        self.assertNotIn(self.service_rental2, qs)
        self.assertEqual(qs.count(), 1)

    def test_billing_account_queryset_with_corporate_ban(self):
        """Test fetching all services of parent account"""
        qs = ServiceRentals.objects.billing_account(self.parent_account)
        self.assertIn(self.service_rental1, qs)
        self.assertIn(self.service_rental2, qs)
        self.assertEqual(qs.count(), 2)

    def test_total_pending_rental(self):
        """Test total_pending_rental calculation"""
        total_pending_rental = ServiceRentals.objects.total_pending_rental()
        self.assertEqual(total_pending_rental, 200)
        self.assertIsInstance(total_pending_rental, Decimal)

        total_pending_rental = ServiceRentals.objects.filter(
            service=self.service1
        ).total_pending_rental()
        self.assertEqual(total_pending_rental, 100)
        self.assertIsInstance(total_pending_rental, Decimal)

    def test_total_pending_rental_with_active_row(self):
        # Test pending_rental for inactive row
        ServiceRentalFactory(
            service=self.service1,
            pending_rental=200,
            status=ServiceRentalStatusEnum.INACTIVE.value,
        )
        total_pending_rental = ServiceRentals.objects.filter(
            service=self.service1
        ).total_pending_rental()
        self.assertEqual(total_pending_rental, 100)


class TestOtherChargesQuerySet(TestCase):
    def setUp(self):
        self.other_charge1 = OtherChargesFactory(
            charge=100, status=OtherChargesStatusEnum.UNPAID.value
        )
        self.other_charge2 = OtherChargesFactory(
            charge=100, status=OtherChargesStatusEnum.UNPAID.value
        )

    def test_billing_account_queryset(self):
        parent_account = BillingAccountFactory()
        self.other_charge1.service.billing_account.parent = parent_account
        self.other_charge1.service.billing_account.save()

        self.other_charge2.service.billing_account.parent = parent_account
        self.other_charge2.service.billing_account.save()

        qs = OtherCharges.objects.billing_account(
            self.other_charge1.service.billing_account
        )
        self.assertIn(self.other_charge1, qs)
        self.assertNotIn(self.other_charge2, qs)
        self.assertEqual(qs.count(), 1)

    def test_billing_account_queryset_with_corporate_ban(self):
        """Test fetching all other charges of parent account"""
        parent_account = BillingAccountFactory()
        self.other_charge1.service.billing_account.parent = parent_account
        self.other_charge1.service.billing_account.save()

        self.other_charge2.service.billing_account.parent = parent_account
        self.other_charge2.service.billing_account.save()
        qs = OtherCharges.objects.billing_account(parent_account)
        self.assertIn(self.other_charge1, qs)
        self.assertIn(self.other_charge2, qs)
        self.assertEqual(qs.count(), 2)

    def test_total_other_charges(self):
        """Test total_other_charges calculation"""
        total_other_charges = OtherCharges.objects.total_other_charges()
        self.assertEqual(total_other_charges, 200)
        self.assertIsInstance(total_other_charges, Decimal)

        total_other_charges = OtherCharges.objects.filter(
            service=self.other_charge1.service
        ).total_other_charges()
        self.assertEqual(total_other_charges, 100)
        self.assertIsInstance(total_other_charges, Decimal)


class TestServiceNumberManager(TestCase):
    def setUp(self):
        self.service_number1 = ServiceNumberFactory(
            number_cost=100, is_paid=ServiceNumberPaidEnum.UNPAID.value
        )
        self.service_number2 = ServiceNumberFactory(
            number_cost=100, is_paid=ServiceNumberPaidEnum.UNPAID.value
        )

    def test_billing_account_queryset(self):
        parent_account = BillingAccountFactory()
        self.service_number1.service.billing_account.parent = parent_account
        self.service_number1.service.billing_account.save()

        self.service_number2.service.billing_account.parent = parent_account
        self.service_number2.service.billing_account.save()

        qs = ServiceNumbers.entries.billing_account(
            self.service_number1.service.billing_account
        )
        self.assertIn(self.service_number1, qs)
        self.assertNotIn(self.service_number2, qs)
        self.assertEqual(qs.count(), 1)

    def test_billing_account_queryset_with_corporate_ban(self):
        """Test fetching all service numbers of parent account"""
        parent_account = BillingAccountFactory()
        self.service_number1.service.billing_account.parent = parent_account
        self.service_number1.service.billing_account.save()

        self.service_number2.service.billing_account.parent = parent_account
        self.service_number2.service.billing_account.save()
        qs = ServiceNumbers.entries.billing_account(parent_account)
        self.assertIn(self.service_number1, qs)
        self.assertIn(self.service_number2, qs)
        self.assertEqual(qs.count(), 2)

    def test_total_unpaid_number_cost(self):
        """Test total_unpaid_number_cost calculation"""
        total_unpaid_number_cost = (
            ServiceNumbers.entries.total_unpaid_number_cost()
        )
        self.assertEqual(total_unpaid_number_cost, 200)
        self.assertIsInstance(total_unpaid_number_cost, Decimal)

        total_unpaid_number_cost = ServiceNumbers.entries.filter(
            service=self.service_number1.service
        ).total_unpaid_number_cost()
        self.assertEqual(total_unpaid_number_cost, 100)
        self.assertIsInstance(total_unpaid_number_cost, Decimal)


class TestFeatureUsagesLogsQuerySet(TestCase):
    def setUp(self):
        self.feature_usages_log1 = FeatureUsagesLogFactory()
        self.feature_usages_log2 = FeatureUsagesLogFactory()

    def test_service_queryset(self):
        qs = FeatureUsagesLogs.entries.service(self.feature_usages_log1.service)
        self.assertIn(self.feature_usages_log1, qs)
        self.assertNotIn(self.feature_usages_log2, qs)
        self.assertEqual(qs.count(), 1)

    def test_find_max_leg_a_usage_instance(self):
        self.feature_usages_log1.leg_a_use = 8
        self.feature_usages_log1.save()
        FeatureUsagesLogFactory(
            service=self.feature_usages_log1.service,
            package_feature=self.feature_usages_log1.package_feature,
            leg_a_use=9,
            start_date=self.feature_usages_log1.start_date,
        )
        feature_usages_log_3 = FeatureUsagesLogFactory(
            service=self.feature_usages_log1.service,
            package_feature=self.feature_usages_log1.package_feature,
            leg_a_use=10,
            start_date=self.feature_usages_log1.start_date,
        )
        log = FeatureUsagesLogs.entries.find_max_leg_a_usage_instance(
            self.feature_usages_log1.service,
            self.feature_usages_log1.package_feature,
            self.feature_usages_log1.start_date,
        )
        self.assertEqual(feature_usages_log_3, log)
