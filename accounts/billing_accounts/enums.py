from accounts.enums import BaseEnum


class BillingStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class BillingVerificationStateEnum(BaseEnum):
    UNVERIFIED = 1
    SEMI_VERIFIED = 2
    VERIFIED = 3
    SPAM = 4
    FRAUD = 5


class BillingAccountCreditStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class BillingAccountCreditTransType(BaseEnum):
    CREDIT = "cr"
    DEBIT = "dr"


class DiscountBucketStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ContactStatusEnums(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ContactTypeEnums(BaseEnum):
    BILLING = 1
    GENERAL = 2
    TECH = 4
