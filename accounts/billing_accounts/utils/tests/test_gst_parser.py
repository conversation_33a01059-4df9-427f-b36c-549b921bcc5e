import pytest

from accounts.billing_accounts.utils.gst_parser import GSTDetail, SurepassGSTParser


class TestGSTDetail:
    """Test cases for GSTDetail class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.sample_data = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, Mumbai, 400001"
                }
            }
        }
        
        self.gst_detail = GSTDetail(
            data=self.sample_data,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street, Mumbai, 400001",
            status="Active"
        )

    def test_init(self):
        """Test GSTDetail initialization."""
        assert self.gst_detail.data == self.sample_data
        assert self.gst_detail.gstin == "27**********1ZV"
        assert self.gst_detail.legal_name == "Test Company Pvt Ltd"
        assert self.gst_detail.pincode == "400001"
        assert self.gst_detail.address == "123 Test Street, Mumbai, 400001"
        assert self.gst_detail.status == "Active"

    def test_repr(self):
        """Test GSTDetail string representation."""
        repr_str = repr(self.gst_detail)
        
        assert "GSTDetail" in repr_str
        assert "27**********1ZV" in repr_str
        assert "Active" in repr_str
        assert "400001" in repr_str

    def test_is_active_true(self):
        """Test is_active method returns True for active status."""
        assert self.gst_detail.is_active() is True

    def test_is_active_false_inactive(self):
        """Test is_active method returns False for inactive status."""
        inactive_detail = GSTDetail(
            data=self.sample_data,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street, Mumbai, 400001",
            status="Inactive"
        )
        
        assert inactive_detail.is_active() is False

    def test_is_active_false_cancelled(self):
        """Test is_active method returns False for cancelled status."""
        cancelled_detail = GSTDetail(
            data=self.sample_data,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street, Mumbai, 400001",
            status="Cancelled"
        )
        
        assert cancelled_detail.is_active() is False

    def test_is_active_case_insensitive(self):
        """Test is_active method is case insensitive."""
        # Test various cases
        for status in ["ACTIVE", "active", "Active", "AcTiVe"]:
            detail = GSTDetail(
                data=self.sample_data,
                gstin="27**********1ZV",
                legal_name="Test Company Pvt Ltd",
                pincode="400001",
                address="123 Test Street, Mumbai, 400001",
                status=status
            )
            assert detail.is_active() is True

    def test_pan_property(self):
        """Test PAN extraction from GSTIN."""
        assert self.gst_detail.pan == "**********"

    def test_pan_property_different_gstin(self):
        """Test PAN extraction with different GSTIN."""
        different_detail = GSTDetail(
            data=self.sample_data,
            gstin="29**********5Z6",
            legal_name="Another Company",
            pincode="560001",
            address="456 Another Street",
            status="Active"
        )
        
        assert different_detail.pan == "**********"

    def test_state_code_property(self):
        """Test state code extraction from GSTIN."""
        assert self.gst_detail.state_code == "27"

    def test_state_code_property_different_gstin(self):
        """Test state code extraction with different GSTIN."""
        different_detail = GSTDetail(
            data=self.sample_data,
            gstin="29**********5Z6",
            legal_name="Another Company",
            pincode="560001",
            address="456 Another Street",
            status="Active"
        )
        
        assert different_detail.state_code == "29"

    def test_mobile_property(self):
        """Test mobile number extraction from data."""
        assert self.gst_detail.mobile == "**********"

    def test_mobile_property_missing_contact_details(self):
        """Test mobile property when contact_details is missing."""
        data_without_contact = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active"
        }
        
        detail = GSTDetail(
            data=data_without_contact,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.mobile == ""

    def test_mobile_property_missing_principal(self):
        """Test mobile property when principal is missing."""
        data_without_principal = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {}
        }
        
        detail = GSTDetail(
            data=data_without_principal,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.mobile == ""

    def test_mobile_property_missing_mobile(self):
        """Test mobile property when mobile is missing."""
        data_without_mobile = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {
                "principal": {
                    "address": "123 Test Street"
                }
            }
        }
        
        detail = GSTDetail(
            data=data_without_mobile,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.mobile == ""

    def test_mobile_property_empty_mobile(self):
        """Test mobile property when mobile is empty string."""
        data_with_empty_mobile = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {
                "principal": {
                    "mobile": "",
                    "address": "123 Test Street"
                }
            }
        }
        
        detail = GSTDetail(
            data=data_with_empty_mobile,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.mobile == ""

    def test_mobile_property_none_mobile(self):
        """Test mobile property when mobile is None."""
        data_with_none_mobile = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {
                "principal": {
                    "mobile": None,
                    "address": "123 Test Street"
                }
            }
        }
        
        detail = GSTDetail(
            data=data_with_none_mobile,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.mobile == ""

    def test_data_attribute_preservation(self):
        """Test that the original data is preserved."""
        # Modify the original data
        modified_data = self.sample_data.copy()
        modified_data["new_field"] = "new_value"
        
        detail = GSTDetail(
            data=modified_data,
            gstin="27**********1ZV",
            legal_name="Test Company Pvt Ltd",
            pincode="400001",
            address="123 Test Street",
            status="Active"
        )
        
        assert detail.data["new_field"] == "new_value"
        assert detail.data == modified_data


class TestSurepassGSTParser:
    """Test cases for SurepassGSTParser class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.sample_data = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, Mumbai, Maharashtra, 400001"
                }
            }
        }

    def test_parse_complete_data(self):
        """Test parsing with complete data."""
        parser = SurepassGSTParser(self.sample_data)
        gst_detail = parser.parse()
        
        assert isinstance(gst_detail, GSTDetail)
        assert gst_detail.data == self.sample_data
        assert gst_detail.gstin == "27**********1ZV"
        assert gst_detail.legal_name == "Test Company Pvt Ltd"
        assert gst_detail.status == "Active"
        assert gst_detail.address == "123 Test Street, Mumbai, Maharashtra, 400001"
        assert gst_detail.pincode == "400001"
        assert gst_detail.mobile == "**********"

    def test_parse_missing_contact_details(self):
        """Test parsing when contact_details is missing."""
        data_without_contact = {
            "gstin": "27**********1ZV",
            "legal_name": "Test Company Pvt Ltd",
            "gstin_status": "Active"
        }
        
        parser = SurepassGSTParser(data_without_contact)
        gst_detail = parser.parse()
        
        assert gst_detail.address == ""
        assert gst_detail.pincode == ""

    def test_extract_pincode_valid(self):
        """Test pincode extraction from address."""
        parser = SurepassGSTParser(self.sample_data)
        
        test_addresses = [
            "123 Test Street, Mumbai, 400001",
            "456 Another Road, Delhi 110001 India",
            "789 Some Place, Bangalore, Karnataka, 560001",
            "Plot 123, Sector 45, Gurgaon 122001"
        ]
        
        expected_pincodes = ["400001", "110001", "560001", "122001"]
        
        for address, expected in zip(test_addresses, expected_pincodes):
            pincode = parser.extract_pincode(address)
            assert pincode == expected

    def test_extract_pincode_no_pincode(self):
        """Test pincode extraction when no pincode in address."""
        parser = SurepassGSTParser(self.sample_data)
        
        address_without_pincode = "123 Test Street, Mumbai, Maharashtra"
        pincode = parser.extract_pincode(address_without_pincode)
        
        assert pincode == ""

    def test_extract_pincode_multiple_numbers(self):
        """Test pincode extraction with multiple numbers in address."""
        parser = SurepassGSTParser(self.sample_data)
        
        # Should extract the 6-digit number, not other numbers
        address_with_multiple_numbers = "Plot 123, Street 45, Mumbai 400001, Phone: **********"
        pincode = parser.extract_pincode(address_with_multiple_numbers)
        
        assert pincode == "400001"

    def test_extract_pincode_invalid_length(self):
        """Test pincode extraction with invalid length numbers."""
        parser = SurepassGSTParser(self.sample_data)
        
        # Should not extract 5-digit or 7-digit numbers
        address_with_invalid = "Plot 123, Street 45, Mumbai 40001, Area 4000012"
        pincode = parser.extract_pincode(address_with_invalid)
        
        assert pincode == ""
