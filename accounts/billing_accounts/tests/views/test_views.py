from django.urls import reverse
from rest_framework import status
from django.utils import timezone
from rest_framework.test import APITestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
import unittest.mock as mock
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.core.tests.factories import StateCodeFactory
from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.billing_accounts.enums import (
    BillingStatusEnum,
    BillingVerificationStateEnum,
)


class TestMarkAccountFraudAPIView(APITestCase):
    def setUp(self):
        self.billing = BillingAccountFactory.create()
        self.service = ServiceFactory.create(
            billing_account=self.billing,
            status=1,
            live_status=1,
        )
        ServiceNumberFactory.create(
            service=self.service,
            service_number="1234",
        )
        ServicePackageFactory.create(
            service=self.service,
        )
        UserProfileFactory(email="<EMAIL>")

    def test_mark_fraud_billing_account_404(
        self,
    ):

        url = reverse(
            "billing_accounts:mark_fraud",
            kwargs={"billing_account_id": "1234"},
        )

        response = self.client.post(
            url,
            {"user_email": "<EMAIL>"},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Not found."

    def test_mark_fraud_billing_account_service_404(
        self,
    ):
        billing = BillingAccountFactory.create()
        url = reverse(
            "billing_accounts:mark_fraud",
            kwargs={"billing_account_id": billing.id},
        )

        response = self.client.post(
            url,
            {"user_email": "<EMAIL>"},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Not found."

    def test_mark_fraud_validation_error(
        self,
    ):
        url = reverse(
            "billing_accounts:mark_fraud",
            kwargs={"billing_account_id": self.billing.id},
        )

        response = self.client.post(
            url,
            {"user_email": "abc"},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @mock.patch("accounts.services.events.ServiceDeactivationEvent.send")
    @mock.patch("accounts.billing_accounts.events.FlaggedFraudEvent.send")
    def test_mark_fraud_success(
        self,
        mock_flagged_fraud_event,
        mock_service_deactivation_event,
        mock_deactivate_company,
        mock_deactivate_service_number,
        mock_update_memcache,
    ):

        url = reverse(
            "billing_accounts:mark_fraud",
            kwargs={"billing_account_id": self.billing.id},
        )
        mock_flagged_fraud_event.return_value = True
        mock_service_deactivation_event.return_value = True
        mock_deactivate_company.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_update_memcache.return_value = True
        response = self.client.post(
            url,
            {"user_email": "<EMAIL>"},
            format="json",
        )
        fraud_billing = BillingAccounts.objects.get(id=self.billing.id)
        fraud_service = Services.objects.get(id=self.service.id)

        assert fraud_billing.verification_state == 5
        assert fraud_billing.status == 0
        assert fraud_service.status == 0

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["message"] == "Account marked as fraud"


class TestGstUpdateAPIView(APITestCase):
    def setUp(self):
        state = StateCodeFactory.create()
        self.billing_account = BillingAccountFactory.create(state_id=state.id)

    def test_edit_gst_not_found(self):
        invalid_url = reverse("billing_accounts:update_gst", kwargs={"id": 123})
        response = self.client.put(invalid_url)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["message"] == "Not found."
        assert response.json()["status"] == "error"
        assert "data" not in response.json()

    def test_edit_gst_validation_error(self):
        # test with empty data
        url = reverse(
            "billing_accounts:update_gst",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.put(url)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )

    def test_edit_gst_with_invalid_gst_no(self):
        url = reverse(
            "billing_accounts:update_gst",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.put(
            url,
            data={
                "gst_no": "ABCDE1234FGHI",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )
        assert response.json()["errors"]["gst_no"] == ["Invalid GSTIN number."]

    @mock.patch(
        "accounts.billing_accounts.utils.billing_account.SurepassApi.gstin_details"
    )
    def test_edit_gst_success(self, mock_gstin_details):
        gst_no = "18**********1Z1"
        state_code = "18"
        mock_gstin_details.return_value = GSTDetail(
            gstin=gst_no,
            legal_name="VoiceTree Technologies Private Limited",
            pincode="560001",
            address="123 Test Street, 560001",
            status="Active",
        )

        state = StateCodeFactory.create(
            code_for_gst=state_code, name="Karnataka"
        )
        url = reverse(
            "billing_accounts:update_gst",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.put(
            url,
            data={"gst_no": gst_no},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "GSTIN Updated Successfully."

        edited = BillingAccounts.objects.get(id=self.billing_account.id)
        assert edited.gst_no == gst_no
        assert edited.state_id == state.id
        assert edited.business_pan == "**********"
        assert edited.business_name == "VoiceTree Technologies Private Limited"
        assert edited.business_address == "123 Test Street, 560001"
        assert edited.business_city == "Karnataka"
        assert edited.business_state == "Karnataka"
        assert edited.business_pincode == "560001"
        assert edited.business_country == "India"

    @mock.patch(
        "accounts.billing_accounts.utils.billing_account.SurepassApi.gstin_details"
    )
    def test_edit_gst_inactive_gst_no(self, mock_gstin_details):
        gst_no = "18**********1Z1"
        mock_gstin_details.return_value = GSTDetail(
            gstin=gst_no,
            legal_name="VoiceTree Technologies Private Limited",
            pincode="560001",
            address="123 Test Street, 560001",
            status="Inactive",
        )

        url = reverse(
            "billing_accounts:update_gst",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.put(
            url,
            data={"gst_no": gst_no},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["message"] == "GSTIN is not active."


class TestBillingAccountListAPIView(APITestCase):
    def setUp(self):
        self.url = reverse(
            "billing_accounts:billing_account_list",
        )
        BillingAccountFactory.create(
            ac_number="AC001", business_name="Test Business", status=1
        )

    # success case
    def test_get_billing_accounts_success(self):
        test_data = {
            "sort": "ac_number",
            "order": "asc",
            "ac_number": "AC001",
            "business_name": "Test Business",
            "status": "active",
        }

        response = self.client.get(self.url, data=test_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["data"][0]["ac_number"], "AC001")
        self.assertEqual(
            response.json()["data"][0]["business_name"], "Test Business"
        )
        self.assertEqual(response.json()["data"][0]["status"], "active")

    # validation error case
    def test_get_billing_accounts_validation_error(self):
        test_data = {
            "sort": "invalid_field",
            "order": "asc",
            "business_name": "Test Business",
            "status": "active",
        }

        response = self.client.get(self.url, data=test_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # Exception case
    def test_get_billing_accounts_error(self):
        test_data = {
            "sort": "ac_number",
            "order": "asc",
            "ac_number": "123456",
            "business_name": "Test Business 123",
            "status": "inactive",
        }

        with mock.patch(
            "accounts.billing_accounts.views.BillingAccountListAPIView.get_queryset"
        ) as mock_get_queryset:
            mock_get_queryset.side_effect = Exception("An error occurred")
            response = self.client.get(self.url, data=test_data)

        self.assertEqual(
            response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    def test_pagination(self):
        BillingAccountFactory.create_batch(15, status=1)
        BillingAccountFactory.create_batch(5, status=0)

        # active records filter
        test_data = {
            "page": 1,
            "sort": "created",
            "order": "desc",
            "status": "active",
        }

        response = self.client.get(self.url, data=test_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("pagination", response.data)
        pagination_data = response.data["pagination"]

        self.assertEqual(pagination_data["count"], 16)
        self.assertEqual(pagination_data["per_page"], 10)
        self.assertEqual(pagination_data["total_pages"], 2)
        self.assertEqual(pagination_data["current"], 1)
        self.assertIn("data", response.data)
        self.assertEqual(len(response.data["data"]), 10)

        # inactive records filter
        test_data = {
            "page": 1,
            "sort": "created",
            "order": "desc",
            "status": "inactive",
        }

        response = self.client.get(self.url, data=test_data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("pagination", response.data)
        pagination_data = response.data["pagination"]

        self.assertEqual(pagination_data["count"], 5)
        self.assertEqual(pagination_data["per_page"], 10)
        self.assertEqual(pagination_data["total_pages"], 1)
        self.assertEqual(pagination_data["current"], 1)
        self.assertIn("data", response.data)
        self.assertEqual(len(response.data["data"]), 5)


class TestBillingAccountRetrieveUpdateAPIView(APITestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()

    def test_retrieve_billing_account_success(self):
        expected_data = {
            "id": self.billing_account.id,
            "parent_id": None,
            "ac_number": self.billing_account.ac_number,
            "state_id": self.billing_account.state_id,
            "org_type_id": self.billing_account.org_type_id,
            "gst_no": self.billing_account.gst_no,
            "tan_no": self.billing_account.tan_no,
            "uan": self.billing_account.uan,
            "business_name": self.billing_account.business_name,
            "billing_day": self.billing_account.billing_day,
            "credit_limit": self.billing_account.credit_limit,
            "min_bal": float(self.billing_account.min_bal),
            "recharge_on_min_bal": float(
                self.billing_account.recharge_on_min_bal
            ),
            "auto_bill_email": self.billing_account.auto_bill_email,
            "auto_bill_sms": self.billing_account.auto_bill_sms,
            "cr_limit_email": self.billing_account.cr_limit_email,
            "cr_limit_sms": self.billing_account.cr_limit_sms,
            "business_pan": self.billing_account.business_pan,
            "business_address": self.billing_account.business_address,
            "business_state": self.billing_account.business_state,
            "business_city": self.billing_account.business_city,
            "business_pincode": self.billing_account.business_pincode,
            "business_country": self.billing_account.business_country,
            "business_type": self.billing_account.business_type,
            "billing_property": self.billing_account.billing_property,
            "account_manager_id": self.billing_account.account_manager_id,
            "discount_id": self.billing_account.discount_id,
            "applied_period": self.billing_account.applied_period,
            "verification_state": BillingVerificationStateEnum.get_name(
                self.billing_account.verification_state
            ).lower(),
            "status": BillingStatusEnum.get_name(
                self.billing_account.status
            ).lower(),
            "created": self.billing_account.created.strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
            "modified": self.billing_account.modified.strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
        }
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing account details"
        print(response.json()["data"], expected_data)
        self.assertDictEqual(response.json()["data"], expected_data)

    def test_retrieve_billing_account_not_found(self):
        url = reverse("billing_accounts:retrieve_update", kwargs={"id": 123})
        response = self.client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["message"] == "Not found."
        assert response.json()["status"] == "error"
        assert "data" not in response.json()

    def test_patch_billing_account_success(self):
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.patch(
            url, data={"status": "inactive"}, format="json"
        )
        assert response.status_code == status.HTTP_200_OK
        assert (
            response.json()["message"]
            == "Billing details updated successfully."
        )
        assert response.json()["data"]["status"] == "inactive"

    def test_patch_billing_account_fraud_verification_state(self):
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.patch(
            url, data={"verification_state": "fraud"}, format="json"
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["errors"]["verification_state"] == [
            "Unable to set verification state to 'fraud', Please use the /billing_accounts/:id/mark_fraud API."
        ]

    def test_patch_billing_account_not_found(self):
        url = reverse("billing_accounts:retrieve_update", kwargs={"id": 123})
        response = self.client.patch(
            url, data={"status": "inactive"}, format="json"
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["message"] == "Not found."
        assert response.json()["status"] == "error"
        assert "data" not in response.json()

    def test_patch_billing_account_validation_error(self):
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.patch(
            url,
            data={
                "status": "invalid_status",
                "verification_state": "invalid_verification_state",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )
        assert response.json()["errors"]["status"] == [
            '"invalid_status" is not a valid choice.'
        ]
        assert response.json()["errors"]["verification_state"] == [
            '"invalid_verification_state" is not a valid choice.'
        ]

    def test_patch_billing_account_update_when_gst_added(self):
        self.billing_account.gst_no = "18**********1Z1"
        self.billing_account.save()
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.patch(
            url,
            data={
                "business_name": "Test Business",
                "business_address": "Test Address",
                "business_city": "Test City",
                "business_state": "Test State",
                "business_pincode": "123456",
                "business_country": "India",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["errors"] == {
            "business_name": ["GSTIN updated, Business Name can't be changed"],
            "business_address": ["GSTIN updated, Address can't be changed"],
            "business_city": ["GSTIN updated, City can't be changed"],
            "business_pincode": ["GSTIN updated, Pincode can't be changed"],
            "business_country": ["GSTIN updated, Country can't be changed"],
        }

    def test_patch_billing_account_update_when_gst_added_but_values_are_same(
        self,
    ):
        self.billing_account.gst_no = "18**********1Z1"
        self.billing_account.save()
        url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )
        response = self.client.patch(
            url,
            data={
                "business_name": self.billing_account.business_name,
                "business_address": self.billing_account.business_address,
                "business_city": self.billing_account.business_city,
                "business_state": self.billing_account.business_state,
                "business_pincode": self.billing_account.business_pincode,
                "business_country": self.billing_account.business_country,
            },
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
