exclude: 'docs|migrations|.git|.tox|venv'
default_stages: [commit]
fail_fast: true

repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        files: (^|/)a/.+\.(py|html|sh|css|js)$

-   repo: local
    hooks:
      - id: black
        name: black
        entry: black
        language: python
        types: [python]

      - id: flake8
        name: flake8
        entry: flake8
        language: python
        types: [python]

      # - id: run-tests
      #   name: Run Tests
      #   entry: pytest -v
      #   language: system
      #   pass_filenames: false
      #   stages: [commit]
